{"servers": {"github": {"type": "http", "url": "https://api.githubcopilot.com/mcp/"}, "figma": {"type": "sse", "url": "http://127.0.0.1:3845/sse"}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "sentry": {"type": "http", "url": "https://mcp.sentry.dev/mcp"}, "deepwiki": {"type": "sse", "url": "https://mcp.deepwiki.com/sse"}, "markitdown": {"command": "docker", "args": ["run", "--rm", "-i", "markitdown-mcp:latest"]}, "context7": {"type": "sse", "url": "https://mcp.context7.com/sse"}, "kubernetes": {"command": "npx", "args": ["mcp-server-kubernetes"]}}, "inputs": []}