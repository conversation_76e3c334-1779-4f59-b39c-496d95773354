package com.example;

import io.micrometer.core.annotation.Timed;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.Map;

/**
 * Controller chính xử lý các REST API endpoint với enhanced error handling và monitoring.
 * Áp dụng Clean Code principles và Spring Boot best practices.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
@Validated
public class HelloController {
    
    /**
     * JDBC Template để thực hiện các truy vấn database.
     */
    private final NamedParameterJdbcTemplate jdbcTemplate;

    /**
     * Health check endpoint cơ bản.
     * 
     * @return status message với timestamp
     */
    @GetMapping("/health")
    @Timed("hello.health")
    public ResponseEntity<Map<String, Object>> health() {
        log.debug("Health check endpoint accessed");
        
        Map<String, Object> response = Map.of(
            "status", "UP",
            "message", "Hello World API is running",
            "timestamp", System.currentTimeMillis()
        );
        
        return ResponseEntity.ok(response);
    }

    /**
     * Lớp kết quả cho phép tính toán với enhanced structure.
     * Chứa thông tin hai số và kết quả tính toán.
     */
    @Data
    static class CalculationResult {
        /** Số thứ nhất trong phép tính */
        private final int left;
        /** Số thứ hai trong phép tính */
        private final int right;
        /** Kết quả của phép tính */
        private final long answer;
        /** Operation type */
        private final String operation;
        /** Timestamp của calculation */
        private final long timestamp;
    }

    /**
     * Endpoint thực hiện phép cộng hai số với validation và monitoring.
     * Áp dụng input validation và error handling best practices.
     * 
     * @param left số thứ nhất (giới hạn từ -1000 đến 1000)
     * @param right số thứ hai (giới hạn từ -1000 đến 1000)
     * @return đối tượng CalculationResult chứa thông tin phép tính và kết quả
     */
    @GetMapping(value = "/calculate", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed("hello.calculate")
    public ResponseEntity<CalculationResult> calculate(
            @RequestParam 
            @Min(value = -1000, message = "Toán hạng trái phải lớn hơn hoặc bằng -1000")
            @Max(value = 1000, message = "Toán hạng trái phải nhỏ hơn hoặc bằng 1000")
            int left,

            @RequestParam
            @Min(value = -1000, message = "Toán hạng phải phải lớn hơn hoặc bằng -1000")
            @Max(value = 1000, message = "Toán hạng phải phải nhỏ hơn hoặc bằng 1000")
            int right) {
        
        log.info("Calculating: {} + {}", left, right);
        
        try {
            MapSqlParameterSource source = new MapSqlParameterSource()
                    .addValue("left", left)
                    .addValue("right", right);
            
            Long result = jdbcTemplate.queryForObject(
                "SELECT :left + :right AS answer", 
                source,
                (rs, rowNum) -> rs.getLong("answer")
            );
            
            CalculationResult calculationResult = new CalculationResult(
                left, right, result, "addition", System.currentTimeMillis()
            );
            
            log.debug("Calculation completed: {} + {} = {}", left, right, result);
            return ResponseEntity.ok(calculationResult);
            
        } catch (Exception e) {
            log.error("Error during calculation: {} + {} - {}", left, right, e.getMessage());
            throw new RuntimeException("Tính toán thất bại", e);
        }
    }

    /**
     * Legacy endpoint để maintain backward compatibility
     */
    @GetMapping(value = "/calc", produces = MediaType.APPLICATION_JSON_VALUE)
    @Deprecated
    public CalculationResult calcLegacy(@RequestParam int left, @RequestParam int right) {
        log.warn("Legacy calc endpoint used - consider migrating to /calculate");
        ResponseEntity<CalculationResult> response = calculate(left, right);

        // Kiểm tra response và body để tránh NullPointerException
        if (response == null || response.getBody() == null) {
            throw new RuntimeException("Không thể lấy kết quả tính toán");
        }

        return response.getBody();
    }
}
