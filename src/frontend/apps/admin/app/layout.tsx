import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";

import "@workspace/ui/globals.css";
import { Providers } from "@/ui-components";

/** Font Geist cho chữ sans-serif */
const fontSans = Geist({
  subsets: ["latin"],
  variable: "--font-sans",
});

/** Font Geist Mono cho chữ monospace */
const fontMono = Geist_Mono({
  subsets: ["latin"],
  variable: "--font-mono",
});

/**
 * Props cho RootLayout component
 */
interface RootLayoutProps {
  /** React components con */
  children: React.ReactNode;
}

/**
 * Root layout component cho admin app
 * Cung cấp cấu trúc HTML cơ bản và các providers cho toàn bộ ứng dụng
 * 
 * @param children - React components con sẽ được render trong body
 */
export default function RootLayout({
  children,
}: Readonly<RootLayoutProps>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${fontSans.variable} ${fontMono.variable} font-sans antialiased `}
      >
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
