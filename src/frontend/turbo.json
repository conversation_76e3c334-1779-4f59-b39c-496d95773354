{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["^test"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "**/*.test.tsx", "**/*.test.ts", "jest.config.js"], "outputs": ["coverage/**"]}, "test:coverage": {"dependsOn": ["^test:coverage"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "**/*.test.tsx", "**/*.test.ts", "jest.config.js"], "outputs": ["coverage/**"]}}}