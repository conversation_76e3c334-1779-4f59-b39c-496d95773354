/// <reference types="@testing-library/jest-dom" />

declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveTextContent(text: string | RegExp): R;
      toHaveAttribute(name: string, value?: string): R;
      toHaveClass(...classNames: string[]): R;
      toBeVisible(): R;
      toBeDisabled(): R;
      toBeEnabled(): R;
      toHaveFocus(): R;
      toBeChecked(): R;
      toHaveValue(value: string | number): R;
      toBeEmptyDOMElement(): R;
      toContainElement(element: HTMLElement): R;
      toContainHTML(html: string): R;
    }
  }
}

export {};