{"name": "@workspace/auth", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint . --max-warnings 0", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "dependencies": {"react": "^19.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "typescript": "^5.7.3"}, "exports": {"./hooks/*": "./src/hooks/*.ts", "./providers/*": "./src/providers/*.tsx"}}