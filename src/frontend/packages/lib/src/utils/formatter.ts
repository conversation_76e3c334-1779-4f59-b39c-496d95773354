/**
 * Format chuỗi ngày tháng thành định dạng dễ đọc
 * 
 * @param dateString - Chuỗi ngày tháng cần format
 * @param locale - Locale để format (mặc định là "en-US")
 * @returns Chuỗi ngày tháng đã được format
 * @throws Error nếu dateString không hợp lệ
 */
export const formatDate = (dateString: string, locale: string = "en-US"): string => {
  const date = new Date(dateString);

  if (isNaN(date.getTime())) {
    throw new Error(`Invalid date string: ${dateString}`);
  }

  return date.toLocaleDateString(locale, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};
