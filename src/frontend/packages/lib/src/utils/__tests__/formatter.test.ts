/**
 * Unit tests cho formatter utility functions
 * Test toàn bộ các functions, conditions và error scenarios
 * Mục tiêu: 100% LOC coverage và 75% branches coverage
 */

import { formatDate } from '../formatter';

describe('Formatter Utils', () => {
  describe('formatDate', () => {
    // Test cases cho valid date strings
    describe('với date strings hợp lệ', () => {
      it('nên format ISO date string với locale mặc định (en-US)', () => {
        const dateString = '2024-01-15T10:30:00.000Z';
        const result = formatDate(dateString);
        expect(result).toBe('January 15, 2024');
      });

      it('nên format ISO date string với locale tiếng Việt', () => {
        const dateString = '2024-12-25T15:45:30.000Z';
        const result = formatDate(dateString, 'vi-VN');
        expect(result).toBe('25 tháng 12, 2024');
      });

      it('nên format date string dạng YYYY-MM-DD', () => {
        const dateString = '2023-07-04';
        const result = formatDate(dateString);
        expect(result).toBe('July 4, 2023');
      });

      it('nên format date string dạng MM/DD/YYYY', () => {
        const dateString = '03/14/2025';
        const result = formatDate(dateString);
        expect(result).toBe('March 14, 2025');
      });

      it('nên format date string với multiple locales', () => {
        const dateString = '2024-06-01T00:00:00.000Z';
        
        expect(formatDate(dateString, 'en-US')).toBe('June 1, 2024');
        expect(formatDate(dateString, 'en-GB')).toBe('1 June 2024');
        expect(formatDate(dateString, 'de-DE')).toBe('1. Juni 2024');
        expect(formatDate(dateString, 'fr-FR')).toBe('1 juin 2024');
      });

      it('nên format edge case dates', () => {
        // Leap year
        expect(formatDate('2024-02-29')).toBe('February 29, 2024');
        
        // Year boundary
        expect(formatDate('2023-12-31T23:59:59.999Z')).toBe('December 31, 2023');
        expect(formatDate('2024-01-01T00:00:00.000Z')).toBe('January 1, 2024');
        
        // Month boundaries
        expect(formatDate('2024-01-31')).toBe('January 31, 2024');
        expect(formatDate('2024-02-01')).toBe('February 1, 2024');
      });

      it('nên handle ISO date strings correctly', () => {
        const isoString = '2024-05-15T08:30:00.000Z';
        const result = formatDate(isoString);
        expect(result).toMatch(/May \d{1,2}, 2024/);
      });
    });

    // Test cases cho invalid date strings
    describe('với date strings không hợp lệ', () => {
      it('nên throw error với empty string', () => {
        expect(() => formatDate('')).toThrow('Invalid date string: ');
      });

      it('nên throw error với invalid date format', () => {
        const invalidDate = 'not-a-date';
        expect(() => formatDate(invalidDate)).toThrow(`Invalid date string: ${invalidDate}`);
      });

      it('nên throw error với invalid ISO string', () => {
        const invalidDate = '2024-13-45T25:70:80.000Z';
        expect(() => formatDate(invalidDate)).toThrow(`Invalid date string: ${invalidDate}`);
      });

      it('nên throw error với random text', () => {
        const invalidDate = 'hello world';
        expect(() => formatDate(invalidDate)).toThrow(`Invalid date string: ${invalidDate}`);
      });

      it('nên throw error với partial date strings', () => {
        expect(() => formatDate('not-a-date')).toThrow('Invalid date string: not-a-date');
        expect(() => formatDate('2024-13-45')).toThrow('Invalid date string: 2024-13-45');
        expect(() => formatDate('invalid-format')).toThrow('Invalid date string: invalid-format');
      });

      it('nên throw error với null/undefined values (cast to string)', () => {
        expect(() => formatDate('null')).toThrow('Invalid date string: null');
        expect(() => formatDate('undefined')).toThrow('Invalid date string: undefined');
      });
    });

    // Test cases cho default parameters
    describe('với default parameters', () => {
      it('nên sử dụng en-US locale khi không truyền locale parameter', () => {
        const dateString = '2024-08-20T12:00:00.000Z';
        const result = formatDate(dateString);
        const expectedResult = formatDate(dateString, 'en-US');
        expect(result).toBe(expectedResult);
      });

      it('nên override default locale khi truyền locale parameter', () => {
        const dateString = '2024-08-20T12:00:00.000Z';
        const resultDefault = formatDate(dateString);
        const resultCustom = formatDate(dateString, 'vi-VN');
        expect(resultDefault).not.toBe(resultCustom);
      });
    });

    // Test cases cho edge cases và boundary conditions
    describe('edge cases và boundary conditions', () => {
      it('nên handle very old dates', () => {
        const oldDate = '1900-01-01';
        const result = formatDate(oldDate);
        expect(result).toBe('January 1, 1900');
      });

      it('nên handle future dates', () => {
        const futureDate = '2100-12-31';
        const result = formatDate(futureDate);
        expect(result).toBe('December 31, 2100');
      });

      it('nên handle Unix epoch', () => {
        const epochDate = '1970-01-01T00:00:00.000Z';
        const result = formatDate(epochDate);
        expect(result).toBe('January 1, 1970');
      });

      it('nên handle date strings với timezone offsets', () => {
        const dateWithTz = '2024-06-15T10:30:00+07:00';
        const result = formatDate(dateWithTz);
        expect(typeof result).toBe('string');
        expect(result).toMatch(/June \d{1,2}, 2024/);
      });

      it('nên handle milliseconds precision', () => {
        const preciseDate = '2024-03-10T14:25:30.123Z';
        const result = formatDate(preciseDate);
        expect(result).toBe('March 10, 2024');
      });
    });

    // Test cases cho error handling và robustness
    describe('error handling và robustness', () => {
      it('nên throw descriptive error messages', () => {
        const invalidInput = 'completely-invalid-date-xyz';
        expect(() => formatDate(invalidInput)).toThrow();
        expect(() => formatDate(invalidInput)).toThrow('Invalid date string');
        expect(() => formatDate(invalidInput)).toThrow(invalidInput);
      });

      it('nên handle string numbers that create invalid dates', () => {
        expect(() => formatDate('999999999999999999')).toThrow();
      });

      it('nên handle special string values', () => {
        expect(() => formatDate('NaN')).toThrow('Invalid date string: NaN');
        expect(() => formatDate('Infinity')).toThrow('Invalid date string: Infinity');
        expect(() => formatDate('-Infinity')).toThrow('Invalid date string: -Infinity');
      });
    });

    // Test cases cho locale validation
    describe('locale parameter validation', () => {
      it('nên work với valid locale codes', () => {
        const dateString = '2024-01-01';
        const validLocales = ['en-US', 'en-GB', 'vi-VN', 'de-DE', 'fr-FR', 'ja-JP', 'ko-KR'];
        
        validLocales.forEach(locale => {
          expect(() => formatDate(dateString, locale)).not.toThrow();
          const result = formatDate(dateString, locale);
          expect(typeof result).toBe('string');
          expect(result.length).toBeGreaterThan(0);
        });
      });

      it('nên fallback gracefully với invalid locale codes', () => {
        const dateString = '2024-01-01';
        // Browser thường fallback to default locale thay vì throw error
        expect(() => formatDate(dateString, 'invalid-locale')).not.toThrow();
        const result = formatDate(dateString, 'invalid-locale');
        expect(typeof result).toBe('string');
      });
    });
  });
});