// Mock for class-variance-authority
module.exports = {
  cva: function(base, config) {
    return function(variants) {
      const variantClass = variants?.variant ? `variant-${variants.variant}` : 'variant-default';
      const sizeClass = variants?.size ? `size-${variants.size}` : 'size-default';
      const customClass = variants?.className || '';
      return `${base} ${variantClass} ${sizeClass} ${customClass}`.trim();
    };
  },
};