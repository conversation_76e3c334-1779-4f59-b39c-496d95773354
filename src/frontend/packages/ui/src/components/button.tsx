import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@workspace/ui/lib/utils"

/**
 * <PERSON><PERSON><PERSON> nghĩa các biến thể style cho Button component sử dụng class-variance-authority
 * Bao gồm các variant khác nhau và kích thước của button
 */
const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      /** <PERSON><PERSON><PERSON> kiểu button kh<PERSON>c nhau */
      variant: {
        /** Button mặc định với màu primary */
        default:
          "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
        /** Button nguy hiểm với màu đỏ */
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",
        /** Button có viền outline */
        outline:
          "border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",
        /** Button phụ với màu secondary */
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        /** Button trong suốt */
        ghost: "hover:bg-accent hover:text-accent-foreground",
        /** Button dạng link */
        link: "text-primary underline-offset-4 hover:underline",
      },
      /** Các kích thước button khác nhau */
      size: {
        /** Kích thước mặc định */
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        /** Kích thước nhỏ */
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        /** Kích thước lớn */
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        /** Kích thước icon vuông */
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

/**
 * Thuộc tính cho Button component
 */
interface ButtonProps extends React.ComponentProps<"button">, VariantProps<typeof buttonVariants> {
  /** Có render thành child element hay không */
  asChild?: boolean
}

/**
 * Component Button có thể tùy chỉnh với nhiều variant và size khác nhau
 * Hỗ trợ các accessibility features và có thể render thành element khác thông qua asChild
 * 
 * @param className - Class CSS bổ sung
 * @param variant - Kiểu button (default, destructive, outline, secondary, ghost, link)
 * @param size - Kích thước button (default, sm, lg, icon)
 * @param asChild - Nếu true, sẽ render thành child element thay vì button
 * @param props - Các props khác của button element
 * @returns JSX.Element
 */
function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: ButtonProps) {
  /** Chọn component để render: Slot nếu asChild=true, ngược lại là button */
  const Comp = asChild ? Slot : "button"

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  )
}

export { Button, buttonVariants }
