/**
 * Unit tests toàn diện cho Button component  
 * Test coverage mục tiêu: 100% LOC và 75% branches
 * Sử dụng simple testing approach không dependency phức tạp
 */

// Mock React using Jest
jest.mock('react', () => {
  const actualReact = jest.requireActual('react');
  return {
    ...actualReact,
    createElement: jest.fn((type, props, ...children) => ({
      type,
      props: { ...props, children },
      key: props?.key || null,
    })),
    forwardRef: jest.fn((fn) => fn),
  };
});

// Import button - mocks are handled by jest config
const { Button, buttonVariants } = require('../button');

describe('Button Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic rendering tests', () => {
    test('renders button element correctly', () => {
      const element = Button({
        children: 'Test Button',
      });

      expect(element.type).toBe('button');
      expect(element.props.children).toBe('Test Button');
      expect(element.props['data-slot']).toBe('button');
    });

    test('applies default variant and size', () => {
      const element = Button({
        children: 'Default Button',
      });

      expect(element.props.className).toContain('variant-default');
      expect(element.props.className).toContain('size-default');
    });

    test('applies custom className', () => {
      const element = Button({
        children: 'Custom Class',
        className: 'custom-class',
      });

      expect(element.props.className).toContain('custom-class');
    });
  });

  describe('Variant prop testing', () => {
    const variants = ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'];

    variants.forEach((variant) => {
      test(`renders with variant="${variant}"`, () => {
        const element = Button({
          variant,
          children: `Button ${variant}`,
        });

        expect(element.props.className).toContain(`variant-${variant}`);
      });
    });
  });

  describe('Size prop testing', () => {
    const sizes = ['default', 'sm', 'lg', 'icon'];

    sizes.forEach((size) => {
      test(`renders with size="${size}"`, () => {
        const element = Button({
          size,
          children: `Button ${size}`,
        });

        expect(element.props.className).toContain(`size-${size}`);
      });
    });
  });

  describe('AsChild prop testing', () => {
    test('renders button element when asChild=false', () => {
      const element = Button({
        asChild: false,
        children: 'Normal Button',
      });

      expect(element.type).toBe('button');
    });

    test('renders Slot component when asChild=true', () => {
      const element = Button({
        asChild: true,
        children: 'Slot Button',
      });

      expect(element.type).toBe('div');
      expect(element.props['data-testid']).toBe('slot');
    });

    test('defaults to button when asChild is undefined', () => {
      const element = Button({
        children: 'Default asChild',
      });

      expect(element.type).toBe('button');
    });
  });

  describe('Props forwarding', () => {
    test('forwards standard button props', () => {
      const props = {
        id: 'test-button',
        'data-testid': 'forwarded-button',
        title: 'Test Title',
        disabled: true,
        onClick: jest.fn(),
      };

      const element = Button({
        ...props,
        children: 'Forwarded Props',
      });

      expect(element.props.id).toBe('test-button');
      expect(element.props['data-testid']).toBe('forwarded-button');
      expect(element.props.title).toBe('Test Title');
      expect(element.props.disabled).toBe(true);
      expect(element.props.onClick).toBe(props.onClick);
    });

    test('forwards aria attributes', () => {
      const element = Button({
        'aria-label': 'Close dialog',
        'aria-pressed': 'true',
        'aria-expanded': 'false',
        children: '×',
      });

      expect(element.props['aria-label']).toBe('Close dialog');
      expect(element.props['aria-pressed']).toBe('true');
      expect(element.props['aria-expanded']).toBe('false');
    });
  });

  describe('Complex combinations', () => {
    test('combines all props together', () => {
      const onClick = jest.fn();
      
      const element = Button({
        variant: 'destructive',
        size: 'lg',
        className: 'custom-class',
        disabled: false,
        onClick: onClick,
        'aria-label': 'Complex button',
        'data-testid': 'complex-button',
        children: 'Complex Button',
      });

      expect(element.props.className).toContain('variant-destructive');
      expect(element.props.className).toContain('size-lg');
      expect(element.props.className).toContain('custom-class');
      expect(element.props.disabled).toBe(false);
      expect(element.props.onClick).toBe(onClick);
      expect(element.props['aria-label']).toBe('Complex button');
      expect(element.props['data-testid']).toBe('complex-button');
    });

    test('asChild with variant and size', () => {
      const element = Button({
        asChild: true,
        variant: 'outline',
        size: 'sm',
        className: 'as-child-class',
        children: 'Link as Button',
      });

      expect(element.type).toBe('div');
      expect(element.props['data-testid']).toBe('slot');
      expect(element.props.className).toContain('variant-outline');
      expect(element.props.className).toContain('size-sm');
      expect(element.props.className).toContain('as-child-class');
    });
  });

  describe('Edge cases', () => {
    test('handles null children', () => {
      const element = Button({
        children: null,
      });

      expect(element.props.children).toBe(null);
      expect(element.type).toBe('button');
    });

    test('handles undefined children', () => {
      const element = Button({
        children: undefined,
      });

      expect(element.props.children).toBe(undefined);
    });

    test('handles boolean children', () => {
      const element = Button({
        children: false,
      });

      expect(element.props.children).toBe(false);
    });

    test('handles number children', () => {
      const element = Button({
        children: 42,
      });

      expect(element.props.children).toBe(42);
    });

    test('handles array children', () => {
      const children = ['First', ' ', 'Second'];
      const element = Button({
        children: children,
      });

      expect(element.props.children).toEqual(children);
    });

    test('handles empty className', () => {
      const element = Button({
        className: '',
        children: 'Empty Class',
      });

      expect(element.props.className).toContain('variant-default');
      expect(element.props.className).toContain('size-default');
    });

    test('handles undefined className', () => {
      const element = Button({
        className: undefined,
        children: 'Undefined Class',
      });

      expect(element.props.className).toContain('variant-default');
      expect(element.props.className).toContain('size-default');
    });
  });

  describe('Type safety and props validation', () => {
    test('accepts all valid HTML button attributes', () => {
      const props = {
        type: 'submit',
        form: 'test-form',
        formAction: '/submit',
        formMethod: 'post',
        formNoValidate: true,
        formTarget: '_blank',
        name: 'submit-button',
        value: 'submit-value',
      };

      const element = Button({
        ...props,
        children: 'Submit Button',
      });

      expect(element.props.type).toBe('submit');
      expect(element.props.form).toBe('test-form');
      expect(element.props.formAction).toBe('/submit');
      expect(element.props.formMethod).toBe('post');
      expect(element.props.formNoValidate).toBe(true);
      expect(element.props.formTarget).toBe('_blank');
      expect(element.props.name).toBe('submit-button');
      expect(element.props.value).toBe('submit-value');
    });

    test('accepts custom data attributes', () => {
      const element = Button({
        'data-custom': 'custom-value',
        'data-test-id': 'test-button',
        'data-cy': 'cypress-button',
        children: 'Data Attributes',
      });

      expect(element.props['data-custom']).toBe('custom-value');
      expect(element.props['data-test-id']).toBe('test-button');
      expect(element.props['data-cy']).toBe('cypress-button');
    });
  });

  describe('Conditional logic coverage', () => {
    test('Component selection logic - asChild true path', () => {
      const element = Button({
        asChild: true,
        children: 'Slot Button',
      });

      // asChild ? Slot : "button" - testing true path
      expect(element.type).toBe('div'); // Slot mocked as div
    });

    test('Component selection logic - asChild false path', () => {
      const element = Button({
        asChild: false,
        children: 'Button Element',
      });

      // asChild ? Slot : "button" - testing false path
      expect(element.type).toBe('button');
    });

    test('Component selection logic - asChild default path', () => {
      const element = Button({
        children: 'Default Button',
      });

      // asChild = false by default - testing default path  
      expect(element.type).toBe('button');
    });

    test('className merging with various inputs', () => {
      // Test với className truthy
      const withClass = Button({
        className: 'test-class',
        children: 'With Class',
      });
      expect(withClass.props.className).toContain('test-class');

      // Test với className falsy
      const withoutClass = Button({
        children: 'Without Class',
      });
      expect(withoutClass.props.className).not.toContain('undefined');
    });

    test('Variant và size với giá trị khác nhau', () => {
      // Test default variants
      const defaultButton = Button({
        children: 'Default',
      });
      expect(defaultButton.props.className).toContain('variant-default');
      expect(defaultButton.props.className).toContain('size-default');

      // Test custom variants
      const customButton = Button({
        variant: 'destructive',
        size: 'lg',
        children: 'Custom',
      });
      expect(customButton.props.className).toContain('variant-destructive');
      expect(customButton.props.className).toContain('size-lg');
    });
  });
});

describe('buttonVariants function', () => {
  test('buttonVariants is exported and callable', () => {
    expect(buttonVariants).toBeDefined();
    expect(typeof buttonVariants).toBe('function');
  });

  test('buttonVariants returns string result', () => {
    const result = buttonVariants();
    expect(typeof result).toBe('string');
  });

  test('buttonVariants with variant parameter', () => {
    const result = buttonVariants({ variant: 'destructive' });
    expect(result).toContain('variant-destructive');
  });

  test('buttonVariants with size parameter', () => {
    const result = buttonVariants({ size: 'lg' });
    expect(result).toContain('size-lg');
  });

  test('buttonVariants with custom className', () => {
    const result = buttonVariants({ className: 'custom-class' });
    expect(result).toContain('custom-class');
  });

  test('buttonVariants with multiple parameters', () => {
    const result = buttonVariants({
      variant: 'outline',
      size: 'sm',
      className: 'multi-class',
    });
    expect(result).toContain('variant-outline');
    expect(result).toContain('size-sm');
    expect(result).toContain('multi-class');
  });
});