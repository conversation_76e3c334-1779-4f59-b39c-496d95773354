/**
 * Unit tests toàn diện cho Button component
 * Test coverage mục tiêu: 100% LOC và 75% branches coverage
 * 
 * Các test cases được thiết kế để cover toàn bộ code paths và scenarios
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button, buttonVariants } from '../button';

// Import global mocks
import * as globalMocks from '../../../../__mocks__/global-mocks.cjs';
const { mockSlot, mockCva, mockCn } = globalMocks;

// Setup component-specific mocks
jest.mock('@radix-ui/react-slot', mockSlot);
jest.mock('class-variance-authority', mockCva);
jest.mock('@workspace/ui/lib/utils', mockCn);

describe('Button Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Render cơ bản - 100% LOC coverage', () => {
    it('renders button với text content', () => {
      render(<Button>Click me</Button>);
      
      const button = screen.getByRole('button', { name: /click me/i });
      expect(button).toBeInTheDocument();
      expect(button.tagName).toBe('BUTTON');
      expect(button).toHaveAttribute('data-slot', 'button');
    });

    it('renders với default props (default variant và size)', () => {
      render(<Button>Default Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass('variant-default');
      expect(button).toHaveClass('size-default');
    });
  });

  describe('Variant prop testing - Coverage tất cả variants', () => {
    const variants = ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'] as const;

    variants.forEach((variant) => {
      it(`renders với variant="${variant}"`, () => {
        render(<Button variant={variant}>Button {variant}</Button>);
        
        const button = screen.getByRole('button');
        expect(button).toHaveClass(`variant-${variant}`);
      });
    });

    it('sử dụng default variant khi không specify', () => {
      render(<Button>No variant</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('variant-default');
    });
  });

  describe('Size prop testing - Coverage tất cả sizes', () => {
    const sizes = ['default', 'sm', 'lg', 'icon'] as const;

    sizes.forEach((size) => {
      it(`renders với size="${size}"`, () => {
        render(<Button size={size}>Button {size}</Button>);
        
        const button = screen.getByRole('button');
        expect(button).toHaveClass(`size-${size}`);
      });
    });

    it('sử dụng default size khi không specify', () => {
      render(<Button>No size</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('size-default');
    });
  });

  describe('AsChild prop testing - Coverage conditional rendering', () => {
    it('renders button element khi asChild=false (default)', () => {
      render(<Button asChild={false}>Normal Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button.tagName).toBe('BUTTON');
    });

    it('renders Slot component khi asChild=true', () => {
      render(
        <Button asChild={true}>
          <a href="/test">Link Button</a>
        </Button>
      );
      
      const slot = screen.getByTestId('slot');
      expect(slot).toBeInTheDocument();
      expect(slot).toHaveAttribute('data-slot', 'button');
    });

    it('test conditional logic: const Comp = asChild ? Slot : "button"', () => {
      // Test true branch
      const { rerender } = render(
        <Button asChild={true}>
          <span>As Child True</span>
        </Button>
      );
      
      expect(screen.getByTestId('slot')).toBeInTheDocument();
      
      // Test false branch
      rerender(
        <Button asChild={false}>
          As Child False
        </Button>
      );
      
      expect(screen.getByRole('button')).toBeInTheDocument();
      expect(screen.queryByTestId('slot')).not.toBeInTheDocument();
    });

    it('test default asChild behavior (undefined = false)', () => {
      render(<Button>Default asChild</Button>);
      
      const button = screen.getByRole('button');
      expect(button.tagName).toBe('BUTTON');
    });
  });

  describe('ClassName merging - Coverage cn function calls', () => {
    it('merge className prop với button variants', () => {
      const customClass = 'custom-class';
      render(<Button className={customClass}>Custom Class</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass(customClass);
      expect(button).toHaveClass('variant-default');
      expect(button).toHaveClass('size-default');
    });

    it('handle empty className', () => {
      render(<Button className="">Empty Class</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('variant-default');
      expect(button).toHaveClass('size-default');
    });

    it('handle undefined className', () => {
      render(<Button className={undefined}>Undefined Class</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('variant-default');
      expect(button).toHaveClass('size-default');
    });

    it('merge multiple custom classes', () => {
      render(<Button className="class1 class2 class3">Multiple Classes</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('class1 class2 class3');
    });
  });

  describe('Props forwarding - Coverage spread operator', () => {
    it('forward standard button props', () => {
      const props = {
        id: 'test-button',
        'data-testid': 'forwarded-button',
        title: 'Test Title',
        'aria-label': 'Test Aria Label',
        disabled: true,
        type: 'submit' as const,
      };

      render(<Button {...props}>Forwarded Props</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('id', 'test-button');
      expect(button).toHaveAttribute('data-testid', 'forwarded-button');
      expect(button).toHaveAttribute('title', 'Test Title');
      expect(button).toHaveAttribute('aria-label', 'Test Aria Label');
      expect(button).toBeDisabled();
      expect(button).toHaveAttribute('type', 'submit');
    });

    it('forward event handlers', () => {
      const onFocus = jest.fn();
      const onBlur = jest.fn();
      const onMouseEnter = jest.fn();
      const onMouseLeave = jest.fn();

      render(
        <Button
          onFocus={onFocus}
          onBlur={onBlur}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
        >
          Event Button
        </Button>
      );
      
      const button = screen.getByRole('button');
      
      fireEvent.focus(button);
      expect(onFocus).toHaveBeenCalledTimes(1);
      
      fireEvent.blur(button);
      expect(onBlur).toHaveBeenCalledTimes(1);
      
      fireEvent.mouseEnter(button);
      expect(onMouseEnter).toHaveBeenCalledTimes(1);
      
      fireEvent.mouseLeave(button);
      expect(onMouseLeave).toHaveBeenCalledTimes(1);
    });
  });

  describe('Click interactions và event handling', () => {
    it('gọi onClick handler khi button được click', async () => {
      const onClick = jest.fn();
      const user = userEvent.setup();
      
      render(<Button onClick={onClick}>Clickable Button</Button>);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      expect(onClick).toHaveBeenCalledTimes(1);
    });

    it('gọi onClick với event object', async () => {
      const onClick = jest.fn();
      const user = userEvent.setup();
      
      render(<Button onClick={onClick}>Click Handler</Button>);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      expect(onClick).toHaveBeenCalledWith(expect.any(Object));
      expect(onClick.mock.calls[0][0]).toHaveProperty('type', 'click');
    });

    it('có thể click multiple times', async () => {
      const onClick = jest.fn();
      const user = userEvent.setup();
      
      render(<Button onClick={onClick}>Multi Click</Button>);
      
      const button = screen.getByRole('button');
      await user.click(button);
      await user.click(button);
      await user.click(button);
      
      expect(onClick).toHaveBeenCalledTimes(3);
    });
  });

  describe('Disabled state - Coverage disabled prop', () => {
    it('render disabled button', () => {
      render(<Button disabled>Disabled Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveAttribute('disabled');
    });

    it('không gọi onClick khi button disabled', async () => {
      const onClick = jest.fn();
      const user = userEvent.setup();
      
      render(<Button disabled onClick={onClick}>Disabled Click</Button>);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      expect(onClick).not.toHaveBeenCalled();
    });

    it('có thể enable/disable dynamically', () => {
      const { rerender } = render(<Button disabled>Toggle Button</Button>);
      
      let button = screen.getByRole('button');
      expect(button).toBeDisabled();
      
      rerender(<Button disabled={false}>Toggle Button</Button>);
      button = screen.getByRole('button');
      expect(button).not.toBeDisabled();
    });
  });

  describe('Accessibility attributes', () => {
    it('có role="button" by default', () => {
      render(<Button>Accessible Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('role', 'button');
    });

    it('hỗ trợ các aria attributes', () => {
      render(
        <Button 
          aria-label="Close dialog"
          aria-pressed="true"
          aria-expanded="false"
          aria-describedby="button-description"
        >
          ×
        </Button>
      );
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Close dialog');
      expect(button).toHaveAttribute('aria-pressed', 'true');
      expect(button).toHaveAttribute('aria-expanded', 'false');
      expect(button).toHaveAttribute('aria-describedby', 'button-description');
    });
  });

  describe('Keyboard interactions', () => {
    it('activate button với Enter key', async () => {
      const onClick = jest.fn();
      const user = userEvent.setup();
      
      render(<Button onClick={onClick}>Keyboard Button</Button>);
      
      const button = screen.getByRole('button');
      button.focus();
      await user.keyboard('{Enter}');
      
      expect(onClick).toHaveBeenCalledTimes(1);
    });

    it('activate button với Space key', async () => {
      const onClick = jest.fn();
      const user = userEvent.setup();
      
      render(<Button onClick={onClick}>Space Button</Button>);
      
      const button = screen.getByRole('button');
      button.focus();
      await user.keyboard(' ');
      
      expect(onClick).toHaveBeenCalledTimes(1);
    });

    it('có thể focus và blur', () => {
      const onFocus = jest.fn();
      const onBlur = jest.fn();
      
      render(
        <Button onFocus={onFocus} onBlur={onBlur}>
          Focus Button
        </Button>
      );
      
      const button = screen.getByRole('button');
      
      button.focus();
      expect(onFocus).toHaveBeenCalledTimes(1);
      expect(button).toHaveFocus();
      
      button.blur();
      expect(onBlur).toHaveBeenCalledTimes(1);
      expect(button).not.toHaveFocus();
    });
  });

  describe('Complex combinations - Integration testing', () => {
    it('combine tất cả props với nhau', () => {
      const onClick = jest.fn();
      
      render(
        <Button
          variant="destructive"
          size="lg"
          className="custom-class"
          disabled={false}
          onClick={onClick}
          aria-label="Complex button"
          data-testid="complex-button"
        >
          Complex Button
        </Button>
      );
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('variant-destructive');
      expect(button).toHaveClass('size-lg');
      expect(button).toHaveClass('custom-class');
      expect(button).not.toBeDisabled();
      expect(button).toHaveAttribute('aria-label', 'Complex button');
      expect(button).toHaveAttribute('data-testid', 'complex-button');
    });

    it('asChild với variant và size', () => {
      render(
        <Button asChild variant="outline" size="sm" className="as-child-class">
          <a href="/link">Link as Button</a>
        </Button>
      );
      
      const slot = screen.getByTestId('slot');
      expect(slot).toHaveClass('variant-outline');
      expect(slot).toHaveClass('size-sm');
      expect(slot).toHaveClass('as-child-class');
    });

    it('multiple event handlers với asChild', () => {
      const onClick = jest.fn();
      const onMouseEnter = jest.fn();
      
      render(
        <Button asChild onClick={onClick} onMouseEnter={onMouseEnter}>
          <button type="button">Custom Button</button>
        </Button>
      );
      
      const slot = screen.getByTestId('slot');
      
      fireEvent.click(slot);
      expect(onClick).toHaveBeenCalledTimes(1);
      
      fireEvent.mouseEnter(slot);
      expect(onMouseEnter).toHaveBeenCalledTimes(1);
    });
  });

  describe('Edge cases - Error handling và boundary testing', () => {
    it('handle null children', () => {
      render(<Button>{null}</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('handle undefined children', () => {
      render(<Button>{undefined}</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('handle boolean children', () => {
      render(<Button>{false}</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('handle number children', () => {
      render(<Button>{42}</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent('42');
    });

    it('handle array children', () => {
      render(<Button>{['First', ' ', 'Second']}</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveTextContent('First Second');
    });

    it('handle React fragment children', () => {
      render(
        <Button>
          <>
            <span>Fragment</span> <span>Children</span>
          </>
        </Button>
      );
      
      const button = screen.getByRole('button');
      expect(button).toHaveTextContent('Fragment Children');
    });
  });

  describe('Type safety và props validation', () => {
    it('accept all valid HTML button attributes', () => {
      const props = {
        type: 'submit' as const,
        form: 'test-form',
        formAction: '/submit',
        formMethod: 'post' as const,
        formNoValidate: true,
        formTarget: '_blank',
        name: 'submit-button',
        value: 'submit-value',
      };

      render(<Button {...props}>Submit Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('type', 'submit');
      expect(button).toHaveAttribute('form', 'test-form');
      expect(button).toHaveAttribute('formaction', '/submit');
      expect(button).toHaveAttribute('formmethod', 'post');
      expect(button).toHaveAttribute('formnovalidate');
      expect(button).toHaveAttribute('formtarget', '_blank');
      expect(button).toHaveAttribute('name', 'submit-button');
      expect(button).toHaveAttribute('value', 'submit-value');
    });

    it('accept custom data attributes', () => {
      render(
        <Button
          data-custom="custom-value"
          data-test-id="test-button"
          data-cy="cypress-button"
        >
          Data Attributes
        </Button>
      );
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('data-custom', 'custom-value');
      expect(button).toHaveAttribute('data-test-id', 'test-button');
      expect(button).toHaveAttribute('data-cy', 'cypress-button');
    });
  });

  describe('Branches coverage - All conditional paths', () => {
    it('buttonVariants với tất cả possible combinations', () => {
      const combinations = [
        { variant: undefined, size: undefined },
        { variant: 'default', size: 'default' },
        { variant: 'destructive', size: 'sm' },
        { variant: 'outline', size: 'lg' },
        { variant: 'secondary', size: 'icon' },
        { variant: 'ghost', size: undefined },
        { variant: 'link', size: 'default' },
      ];

      combinations.forEach(({ variant, size }) => {
        render(
          <Button variant={variant as any} size={size as any}>
            Test {variant}-{size}
          </Button>
        );
      });

      // Verify tất cả buttons được render
      expect(screen.getAllByRole('button')).toHaveLength(combinations.length);
    });

    it('asChild branches với different props', () => {
      // Branch 1: asChild = true
      const { rerender } = render(
        <Button asChild={true}>
          <div>As Child True</div>
        </Button>
      );
      expect(screen.getByTestId('slot')).toBeInTheDocument();

      // Branch 2: asChild = false  
      rerender(
        <Button asChild={false}>
          As Child False
        </Button>
      );
      expect(screen.getByRole('button')).toBeInTheDocument();

      // Branch 3: asChild = undefined (default)
      rerender(
        <Button>
          Default asChild
        </Button>
      );
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('className processing branches', () => {
      // Truthy className
      const { rerender } = render(
        <Button className="truthy-class">Truthy</Button>
      );
      expect(screen.getByRole('button')).toHaveClass('truthy-class');

      // Falsy className (empty string)
      rerender(
        <Button className="">Empty</Button>
      );
      expect(screen.getByRole('button')).not.toHaveClass('');

      // Falsy className (undefined)
      rerender(
        <Button className={undefined}>Undefined</Button>
      );
      expect(screen.getByRole('button')).toBeInTheDocument();

      // Falsy className (null)
      rerender(
        <Button className={null as any}>Null</Button>
      );
      expect(screen.getByRole('button')).toBeInTheDocument();
    });
  });
});

describe('buttonVariants function', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('export buttonVariants function', () => {
    expect(buttonVariants).toBeDefined();
    expect(typeof buttonVariants).toBe('function');
  });

  it('buttonVariants trả về class string', () => {
    const result = buttonVariants();
    expect(typeof result).toBe('string');
  });

  it('buttonVariants với variant parameter', () => {
    const result = buttonVariants({ variant: 'destructive' });
    expect(result).toContain('variant-destructive');
  });

  it('buttonVariants với size parameter', () => {
    const result = buttonVariants({ size: 'lg' });
    expect(result).toContain('size-lg');
  });

  it('buttonVariants với custom className', () => {
    const result = buttonVariants({ className: 'custom-class' });
    expect(result).toContain('custom-class');
  });

  it('buttonVariants với tất cả parameters', () => {
    const result = buttonVariants({
      variant: 'outline',
      size: 'sm',
      className: 'all-params',
    });
    
    expect(result).toContain('variant-outline');
    expect(result).toContain('size-sm');
    expect(result).toContain('all-params');
  });

  it('buttonVariants với empty/undefined parameters', () => {
    const results = [
      buttonVariants({}),
      buttonVariants({ variant: undefined }),
      buttonVariants({ size: undefined }),
      buttonVariants({ className: undefined }),
    ];

    results.forEach(result => {
      expect(typeof result).toBe('string');
      expect(result).toContain('variant-default');
      expect(result).toContain('size-default');
    });
  });
});

/**
 * For detailed coverage summary, edge cases, mocking strategy, and testing best practices,
 * please refer to the TESTING.md file in the project root.
 */