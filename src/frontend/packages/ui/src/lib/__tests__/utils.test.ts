/**
 * Unit tests cho cn utility function
 * Test class name merging với clsx và tailwind-merge
 * Mục tiêu: 100% LOC coverage
 */

import { cn } from '../utils';

// Mock dependencies
jest.mock('clsx', () => ({
  clsx: jest.fn((...args) => args.join(' '))
}));
jest.mock('tailwind-merge', () => ({
  twMerge: jest.fn((input) => `merged(${input})`)
}));

describe('Utils', () => {
  describe('cn function', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    describe('basic functionality', () => {
      it('nên call clsx với inputs và return twMerge result', () => {
        const { clsx } = require('clsx');
        const { twMerge } = require('tailwind-merge');
        
        const result = cn('class1', 'class2');
        
        expect(clsx).toHaveBeenCalledWith(['class1', 'class2']);
        expect(twMerge).toHaveBeenCalledWith('class1 class2');
        expect(result).toBe('merged(class1 class2)');
      });

      it('nên handle single string input', () => {
        const { clsx } = require('clsx');
        const { twMerge } = require('tailwind-merge');
        
        const result = cn('single-class');
        
        expect(clsx).toHaveBeenCalledWith(['single-class']);
        expect(twMerge).toHaveBeenCalledWith('single-class');
        expect(result).toBe('merged(single-class)');
      });

      it('nên handle multiple string inputs', () => {
        const { clsx } = require('clsx');
        const { twMerge } = require('tailwind-merge');
        
        const result = cn('class1', 'class2', 'class3');
        
        expect(clsx).toHaveBeenCalledWith(['class1', 'class2', 'class3']);
        expect(twMerge).toHaveBeenCalledWith('class1 class2 class3');
        expect(result).toBe('merged(class1 class2 class3)');
      });

      it('nên handle no inputs', () => {
        const { clsx } = require('clsx');
        const { twMerge } = require('tailwind-merge');
        
        const result = cn();
        
        expect(clsx).toHaveBeenCalledWith([]);
        expect(twMerge).toHaveBeenCalledWith('');
        expect(result).toBe('merged()');
      });
    });

    describe('input types', () => {
      it('nên handle conditional objects', () => {
        const { clsx } = require('clsx');
        
        cn({ 'active': true, 'disabled': false });
        
        expect(clsx).toHaveBeenCalledWith([{ 'active': true, 'disabled': false }]);
      });

      it('nên handle arrays', () => {
        const { clsx } = require('clsx');
        
        cn(['class1', 'class2']);
        
        expect(clsx).toHaveBeenCalledWith([['class1', 'class2']]);
      });

      it('nên handle undefined values', () => {
        const { clsx } = require('clsx');
        
        cn('class1', undefined, 'class2');
        
        expect(clsx).toHaveBeenCalledWith(['class1', undefined, 'class2']);
      });

      it('nên handle null values', () => {
        const { clsx } = require('clsx');
        
        cn('class1', null, 'class2');
        
        expect(clsx).toHaveBeenCalledWith(['class1', null, 'class2']);
      });

      it('nên handle mixed input types', () => {
        const { clsx } = require('clsx');
        
        const inputs = [
          'string-class',
          { 'conditional': true },
          ['array', 'classes'],
          undefined,
          null
        ];
        
        cn(...inputs);
        
        expect(clsx).toHaveBeenCalledWith(inputs);
      });
    });

    describe('integration với dependencies', () => {
      it('nên pass correct data flow từ clsx đến twMerge', () => {
        const { clsx } = require('clsx');
        const { twMerge } = require('tailwind-merge');
        
        // Mock clsx để return specific value
        clsx.mockReturnValueOnce('processed-classes');
        
        const result = cn('input-class');
        
        expect(clsx).toHaveBeenCalledWith(['input-class']);
        expect(twMerge).toHaveBeenCalledWith('processed-classes');
        expect(result).toBe('merged(processed-classes)');
      });

      it('nên handle empty string từ clsx', () => {
        const { clsx } = require('clsx');
        const { twMerge } = require('tailwind-merge');
        
        clsx.mockReturnValueOnce('');
        
        const result = cn();
        
        expect(twMerge).toHaveBeenCalledWith('');
        expect(result).toBe('merged()');
      });
    });

    describe('rest parameters coverage', () => {
      it('nên handle spread operator với many arguments', () => {
        const { clsx } = require('clsx');
        
        const manyClasses = Array.from({ length: 10 }, (_, i) => `class${i}`);
        cn(...manyClasses);
        
        expect(clsx).toHaveBeenCalledWith(manyClasses);
      });

      it('nên collect all arguments vào inputs array', () => {
        const { clsx } = require('clsx');
        
        const arg1 = 'first';
        const arg2 = { second: true };
        const arg3 = ['third'];
        
        cn(arg1, arg2, arg3);
        
        expect(clsx).toHaveBeenCalledWith([arg1, arg2, arg3]);
      });
    });

    describe('return value', () => {
      it('nên return exactly what twMerge returns', () => {
        const { twMerge } = require('tailwind-merge');
        
        const expectedResult = 'final-merged-classes';
        twMerge.mockReturnValueOnce(expectedResult);
        
        const result = cn('any-input');
        
        expect(result).toBe(expectedResult);
      });

      it('nên return string type', () => {
        const result = cn('test-class');
        
        expect(typeof result).toBe('string');
      });
    });

    describe('function composition', () => {
      it('nên demonstrate correct composition: inputs -> clsx -> twMerge -> result', () => {
        const { clsx } = require('clsx');
        const { twMerge } = require('tailwind-merge');
        
        // Track call order
        const callOrder: string[] = [];
        
        clsx.mockImplementation((...args) => {
          callOrder.push('clsx');
          return 'clsx-result';
        });
        
        twMerge.mockImplementation((input) => {
          callOrder.push('twMerge');
          expect(input).toBe('clsx-result');
          return 'final-result';
        });
        
        const result = cn('test');
        
        expect(callOrder).toEqual(['clsx', 'twMerge']);
        expect(result).toBe('final-result');
      });
    });
  });
});