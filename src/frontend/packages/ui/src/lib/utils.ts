import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Utility function để kết hợp các class names
 * Sử dụng clsx để xử lý conditional classes và twMerge để merge Tailwind classes
 * 
 * @param inputs - Mảng các class values có thể là string, object, array, undefined
 * @returns Chuỗi class names đã được merge và optimized
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
