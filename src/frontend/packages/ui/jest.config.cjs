/** @type {import('jest').Config} */
const baseConfig = require('../../jest.base.cjs');

module.exports = {
  ...baseConfig,
  displayName: '@workspace/ui',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/../../jest.setup.cjs'],
  moduleNameMapper: {
    ...baseConfig.moduleNameMapper,
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', {
      presets: [
        ['@babel/preset-env', { targets: { node: 'current' } }],
        '@babel/preset-typescript',
        ['@babel/preset-react', { runtime: 'automatic' }],
      ],
    }],
  },
  collectCoverageFrom: [
    ...baseConfig.collectCoverageFrom,
    '!src/styles/**',
  ],
};