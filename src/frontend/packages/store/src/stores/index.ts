import { create } from "zustand";

/**
 * Interface định nghĩa state và actions cho Counter store
 */
interface CounterState {
  /** Gi<PERSON> trị hiện tại của counter */
  count: number;
  /** Function để tăng giá trị counter lên 1 */
  increment: () => void;
  /** Function để giảm giá trị counter xuống 1 */
  decrement: () => void;
}

/**
 * Zustand store để quản lý state của counter
 * Cung cấp các actions để tăng/giảm giá trị counter
 */
export const useCounterStore = create<CounterState>((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
}));
