{"name": "@workspace/store", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint . --max-warnings 0", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "dependencies": {"zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^20", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "typescript": "^5.7.3"}, "exports": {"./stores/*": "./src/stores/*.ts", "./hooks/*": "./src/hooks/*.ts", "./lib/*": "./src/lib/*.ts"}}