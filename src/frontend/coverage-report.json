
> frontend-cms@0.0.1 test:coverage /root/repo/src/frontend
> turbo test:coverage --reporter\=json

 ERROR  unexpected argument '--reporter' found

  tip: to pass '--reporter' as a value, use '-- --reporter'

Usage: turbo <--cache-dir <CACHE_DIR>|--concurrency <CONCURRENCY>|--continue[=<CONTINUE>]|--single-package|--framework-inference [<BOOL>]|--global-deps <GLOBAL_DEPS>|--env-mode [<ENV_MODE>]|--filter <FILTER>|--affected|--output-logs <OUTPUT_LOGS>|--log-order <LOG_ORDER>|--only|--pkg-inference-root <PKG_INFERENCE_ROOT>|--log-prefix <LOG_PREFIX>|TASKS|PASS_THROUGH_ARGS>

For more information, try '--help'.

 ELIFECYCLE  Command failed with exit code 1.
