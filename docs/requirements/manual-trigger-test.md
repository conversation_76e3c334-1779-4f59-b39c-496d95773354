# Test File cho Manual Trigger

## <PERSON><PERSON><PERSON> đích
File này được tạo để test chức năng manual trigger của workflow "Đ<PERSON>h giá thay đổi yêu cầu nghiệp vụ".

## Thông tin test
- **Ngày tạo**: 2025-07-31
- **M<PERSON><PERSON> tiêu**: Verify manual trigger hoạt động đúng
- **Test scenario**: Tạo thay đổi trong thư mục requirements để trigger workflow

## Nội dung test

### Test Case 1: Basic Manual Trigger
Đây là nội dung test cơ bản để verify rằng manual trigger có thể detect thay đổi trong thư mục requirements.

### Test Case 2: State Management
Test này verify rằng state management hoạt động đúng:
- Lần chạy đầu tiên sẽ tạo issue
- <PERSON>ần chạy thứ hai (nếu không có thay đổi mới) sẽ không tạo issue duplicate

### Test Case 3: Force Create
Test với option force_create_issue = true để verify rằng issue đượ<PERSON> tạo ngay cả khi không có thay đổi.

## Kết quả mong đợi
- Workflow chạy thành công
- Issue được tạo với đúng format
- State được lưu trữ và sử dụng đúng
- Không có duplicate processing

## Ghi chú
File này có thể được xóa sau khi hoàn thành testing.
