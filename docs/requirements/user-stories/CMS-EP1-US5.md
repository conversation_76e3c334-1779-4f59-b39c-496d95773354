# CMS-EP1-US5 – <PERSON> chuyển chuyên mục

**Epic**: CMS-EP1 – <PERSON><PERSON><PERSON>n lý <PERSON> & <PERSON><PERSON><PERSON><PERSON> mục  
**ID**: CMS-EP1-US5  
**Role**: Là **Admin**  
**Goal**: T<PERSON><PERSON> muốn **di chuyển chuyên mục sang kênh khác**  
**Value**: <PERSON><PERSON> dễ dàng tái cấu trúc nội dung.

## Ưu tiên
M – Trung bình

## Điểm ước lượng
5 điểm

## Acceptance Criteria (Tiêu ch<PERSON> chấp nhận)

```gherkin
Scenario: Di chuyển chuyên mục thành công
  Given chuyên mục "Sách hay" đang thuộc kênh "Review"
  When tôi chọn chuyển sang kênh "Giáo dục" và xác nhận
  Then chuyên mục "Sách hay" hiển thị dưới kênh "<PERSON><PERSON><PERSON><PERSON> dụ<PERSON>" với dữ liệu giữ nguyên
```

## Definition of Done (<PERSON><PERSON><PERSON> nghĩa <PERSON>àn thành)
- <PERSON><PERSON><PERSON> thị hộp thoại xác nhận trước khi di chuyển.
- Kiểm tra quyền thực hiện (chỉ Admin).
- Dữ liệu bài viết vẫn liên kết đúng với chuyên mục sau khi di chuyển.
- Cập nhật hiển thị breadcrumb và API liên quan.
- Đã viết test cho các trường hợp cơ bản.
