# Th<PERSON> mục `/docs`

<PERSON><PERSON><PERSON> là nơi lưu trữ toàn bộ tài liệu của dự án, đ<PERSON><PERSON><PERSON> cấu trúc để dễ dàng tra cứu và quản lý.

Nơi chứa tài liệu chung cho repos, bao gồm tài liệu design của SA, tài liệu nghiệp vụ của BA, tài liệu chứa các lỗi thường gặp.

## Tóm tắt các thư mục con

*   `./architecture`, `./architecture-design`, `./software-design`, `./detailed-design`: Chứa các tài liệu liên quan đến thiết kế kiến trúc và thiết kế phần mềm, từ tổng quan (high-level) đến chi tiết (detailed).
*   `./guidelines`: <PERSON><PERSON> gồm các hướng dẫn, quy ướ<PERSON> (conventions), và các quy trình chuẩn cần tuân thủ trong quá trình phát triển dự án.
*   `./issues`: Tổ<PERSON> hợp các vấn đề (lỗi, bugs) đã xảy ra, kèm theo phân tích nguyên nhân và giải pháp đã được áp dụng.
*   `./requirements`: Chứa các tài liệu về yêu cầu nghiệp vụ, bao gồm product backlog, user stories, và các đặc tả chức năng từ BA.
