# Hướng dẫn sử dụng GitHub Issue Template "Đ<PERSON>h giá thay đổi yêu cầu nghiệp vụ"

## M<PERSON><PERSON> đích

Template issue này được thiết kế để tự động tạo ra khi có thay đổi trong thư mục `docs/requirements`, gi<PERSON>p đánh giá tác động của các thay đổi yêu cầu nghiệp vụ đến dự án.

## Cách hoạt động

### Trigger tự động

GitHub Action sẽ tự động tạo issue khi:

1. **Push trực tiếp** vào branch `main` hoặc `develop` với thay đổi trong `docs/requirements/`
2. **Merge Pull Request** vào branch `main` hoặc `develop` với thay đổi trong `docs/requirements/`

### Thông tin được tự động điền

- **Commit trước và sau** với link đến từng commit
- **<PERSON>h sách file thay đổi** trong thư mục `docs/requirements/`
- **Chi tiết diff** c<PERSON><PERSON> các thay đổi
- **Thời gian phát hiện** và **người thực hiện commit**
- **Mention tự động** cho @copilot và Jules để thực hiện đánh giá

## Cấu trúc đánh giá

### 1. Phân loại thay đổi
- Tính năng mới
- Sửa đổi tính năng cũ  
- Chưa phân loại

### 2. Phân tích tác động
Xác định những user story/epic bị ảnh hưởng bởi thay đổi

### 3. Phân công công việc theo vai trò

#### Designer
- Cập nhật UI/UX design
- Cập nhật wireframe
- Tạo prototype mới

#### System Analyst (SA)
- Cập nhật tài liệu phân tích
- Xem xét lại use case
- Cập nhật business rules

#### Developer
- Cập nhật code
- Thay đổi API
- Cập nhật database schema

#### Tester
- Cập nhật test case
- Cập nhật test plan
- Viết automation test mới

### 4. Đánh giá ưu tiên
- Thấp
- Trung bình
- Cao  
- Khẩn cấp

### 5. Kế hoạch thực hiện
- Schedule meeting với stakeholders
- Review và approve design changes
- Cập nhật development plan
- Thực hiện testing

## Quy trình xử lý issue

### Bước 0: AI Auto-Processing
- **GitHub Copilot** tự động phân tích nội dung thay đổi
- **Jules** xác thực và bổ sung phân tích của Copilot
- Cung cấp draft đánh giá ban đầu trong comment

### Bước 1: Review thông tin tự động
- Kiểm tra commit trước và sau
- Đọc chi tiết thay đổi trong diff
- Xác nhận các file bị ảnh hưởng
- Review kết quả phân tích từ AI

### Bước 2: Phân tích và đánh giá
- Phân loại loại thay đổi (dựa trên gợi ý của AI)
- Xác định user story/epic bị tác động  
- Đánh giá mức độ ưu tiên
- Xác thực hoặc điều chỉnh phân tích của AI

### Bước 3: Phân công công việc
- Đánh dấu các role cần tham gia (dựa trên phân tích AI)
- Mô tả chi tiết công việc cho từng role
- Ước tính effort và timeline
- Assign cho các thành viên phù hợp

### Bước 4: Lập kế hoạch
- Tạo timeline thực hiện
- Schedule các meeting cần thiết  
- Tạo sub-issues nếu cần thiết
- Liên kết với related issues/PRs

### Bước 5: Tracking và follow-up
- Theo dõi tiến độ thực hiện
- Update status định kỳ
- Sync với AI agents về tiến độ
- Close issue khi hoàn thành

## Labels tự động

Issue sẽ được gắn các labels sau:
- `requirements-review`: Đánh dấu là review yêu cầu
- `business-analysis`: Liên quan đến phân tích nghiệp vụ
- `auto-generated`: Được tạo tự động bởi GitHub Actions
- `assign-to-copilot`: Giao việc cho GitHub Copilot thực hiện phân tích
- `assign-to-jules`: Giao việc cho Google Jules đánh giá và xác thực

## Assignees tự động

Issue sẽ được mention tự động cho:
- **@copilot**: GitHub Copilot sẽ thực hiện phân tích tự động và đưa ra gợi ý
- **Jules**: AI agent sẽ đánh giá và xác thực kết quả từ Copilot

## Tùy chỉnh

### Điều chỉnh trigger
Có thể chỉnh sửa file `.github/workflows/requirements-change-review.yml` để:
- Thay đổi branch trigger
- Điều chỉnh path monitor
- Tùy chỉnh điều kiện trigger

### Cập nhật template
Chỉnh sửa file `.github/ISSUE_TEMPLATE/danh-gia-thay-doi-yeu-cau-nghiep-vu.yml` để:
- Thêm/bớt trường thông tin
- Tùy chỉnh dropdown options
- Điều chỉnh labels

### Cải thiện workflow
- Thêm notification qua Slack/Teams
- Integrate với project management tools
- Tự động assign theo rule

## Lưu ý quan trọng

1. **AI Integration**: Issue sẽ tự động mention @copilot để kích hoạt phân tích AI
2. **Permissions**: Workflow cần quyền write để tạo issue và gắn labels
3. **Rate limiting**: GitHub API có giới hạn request per hour
4. **Content length**: Diff content sẽ bị cắt nếu quá dài (>10KB)
5. **Branch protection**: Đảm bảo workflow có thể chạy trên protected branches
6. **AI Workflow**: Copilot và Jules sẽ tự động bắt đầu phân tích khi được mention
7. **Human Validation**: Luôn cần con người xác thực kết quả phân tích từ AI

## Troubleshooting

### Issue không được tạo
- Kiểm tra workflow có trigger đúng không
- Xem logs trong GitHub Actions tab
- Đảm bảo có thay đổi trong `docs/requirements/`

### Thông tin không chính xác
- Kiểm tra commit SHA trong workflow
- Verify repository permissions
- Xem xét cấu hình fetch-depth

### Template hiển thị sai
- Validate YAML syntax của template
- Kiểm tra markdown formatting
- Test với manual issue creation
