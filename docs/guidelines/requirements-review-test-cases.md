# Test Cases cho Requirements Review Manual Trigger

## Test Case 1: Auto Mode - <PERSON><PERSON> thay đổi mới

### M<PERSON><PERSON> tiêu
Verify auto mode hoạt động đúng khi có thay đổi requirements mới từ lần scan cuối

### Preconditions
- Repository có ít nhất 1 commit với thay đổi trong `docs/requirements/`
- Đã có ít nhất 1 lần chạy workflow thành công trước đó

### Test Steps
1. Truy cập GitHub Actions → "Đánh giá thay đổi yêu cầu nghiệp vụ"
2. Click "Run workflow"
3. <PERSON><PERSON><PERSON> hình:
   - Branch: `develop`
   - <PERSON><PERSON> độ quét: `auto`
   - Bắ<PERSON> buộc tạo issue: `false`
4. Click "Run workflow"

### Expected Results
- Workflow chạy thành công
- Tạo issue mới với thay đổi từ lần scan cuối
- <PERSON> được cập nhật với commit hiện tại
- <PERSON>g hiển thị "Auto scan mode: from [last_commit] to HEAD"

---

## Test Case 2: Auto Mode - Không có thay đổi mới

### Mụ<PERSON> tiêu
Verify auto mode không tạo issue khi không có thay đổi mới

### Preconditions
- Đã chạy workflow thành công và không có commit mới sau đó

### Test Steps
1. Chạy workflow với auto mode (như Test Case 1)

### Expected Results
- Workflow chạy thành công
- Không tạo issue mới
- Log hiển thị "No changes in docs/requirements directory"
- Log hiển thị "has_changes=false"

---

## Test Case 3: Full Mode

### Mục tiêu
Verify full mode quét toàn bộ từ commit đầu tiên

### Test Steps
1. Cấu hình:
   - Chế độ quét: `full`
   - Bắt buộc tạo issue: `true`

### Expected Results
- Workflow chạy thành công
- Tạo issue với tất cả thay đổi từ commit đầu tiên
- Log hiển thị "Full scan mode: from [first_commit] to HEAD"
- Issue có thể rất lớn nếu có nhiều thay đổi

---

## Test Case 4: Custom Mode - Valid Range

### Mục tiêu
Verify custom mode với commit range hợp lệ

### Preconditions
- Xác định 2 commit SHA hợp lệ có thay đổi requirements

### Test Steps
1. Cấu hình:
   - Chế độ quét: `custom`
   - Commit bắt đầu: `[commit_sha_1]`
   - Commit kết thúc: `[commit_sha_2]`
   - Bắt buộc tạo issue: `false`

### Expected Results
- Workflow chạy thành công
- Tạo issue với thay đổi trong khoảng commit đã chỉ định
- Log hiển thị "Custom scan mode: from [commit_sha_1] to [commit_sha_2]"

---

## Test Case 5: Custom Mode - Invalid Range

### Mục tiêu
Verify error handling khi commit range không hợp lệ

### Test Steps
1. Cấu hình:
   - Chế độ quét: `custom`
   - Commit bắt đầu: `invalid_commit`
   - Commit kết thúc: `HEAD`

### Expected Results
- Workflow fail với error message rõ ràng
- Log hiển thị lỗi về invalid commit

---

## Test Case 6: Force Create Issue

### Mục tiêu
Verify force create issue hoạt động khi không có thay đổi

### Preconditions
- Không có thay đổi mới trong requirements

### Test Steps
1. Cấu hình:
   - Chế độ quét: `auto`
   - Bắt buộc tạo issue: `true`

### Expected Results
- Workflow chạy thành công
- Tạo issue dù không có thay đổi
- Issue content hiển thị "Không có thay đổi file (manual trigger với force create)"

---

## Test Case 7: State Management

### Mục tiêu
Verify state được lưu và sử dụng đúng

### Test Steps
1. Chạy workflow lần 1 với auto mode
2. Tạo commit mới với thay đổi requirements
3. Chạy workflow lần 2 với auto mode

### Expected Results
- Lần 1: Tạo issue và lưu state
- Lần 2: Chỉ quét từ commit của lần 1, không duplicate
- Cache key `requirements-scan-state-{repository}` được tạo

---

## Test Case 8: Integration với Automatic Trigger

### Mục tiêu
Verify manual và automatic trigger chia sẻ state

### Test Steps
1. Chạy manual trigger
2. Push commit mới với thay đổi requirements (trigger automatic)
3. Chạy manual trigger lần nữa

### Expected Results
- Manual trigger 1: Tạo issue và lưu state
- Automatic trigger: Chỉ quét commit mới, sử dụng state từ manual
- Manual trigger 2: Không tạo issue vì automatic đã xử lý

---

## Test Case 9: Permission và Security

### Mục tiêu
Verify workflow có đủ permissions

### Test Steps
1. Kiểm tra workflow permissions trong file YAML
2. Chạy workflow và verify các actions

### Expected Results
- Permissions bao gồm: contents:read, issues:write, pull-requests:write, actions:write
- Workflow có thể tạo issue, lưu cache, đọc repository

---

## Test Case 10: Error Recovery

### Mục tiêu
Verify workflow handle errors gracefully

### Test Steps
1. Chạy với invalid parameters
2. Chạy khi GitHub API rate limit
3. Chạy khi repository không có requirements folder

### Expected Results
- Clear error messages
- Workflow fails fast với meaningful logs
- Không corrupt state

---

## Automated Testing Script

```bash
#!/bin/bash
# Script để chạy một số test cases tự động

echo "=== Requirements Review Manual Trigger Tests ==="

# Test Case 1: Auto mode
echo "Test 1: Auto mode with changes"
gh workflow run "requirements-change-review.yml" \
  --field scan_mode=auto \
  --field force_create_issue=false

# Test Case 3: Full mode  
echo "Test 3: Full mode"
gh workflow run "requirements-change-review.yml" \
  --field scan_mode=full \
  --field force_create_issue=true

# Test Case 6: Force create
echo "Test 6: Force create issue"
gh workflow run "requirements-change-review.yml" \
  --field scan_mode=auto \
  --field force_create_issue=true

echo "Tests triggered. Check GitHub Actions for results."
```

## Manual Testing Checklist

- [ ] Test Case 1: Auto Mode - Có thay đổi mới
- [ ] Test Case 2: Auto Mode - Không có thay đổi mới  
- [ ] Test Case 3: Full Mode
- [ ] Test Case 4: Custom Mode - Valid Range
- [ ] Test Case 5: Custom Mode - Invalid Range
- [ ] Test Case 6: Force Create Issue
- [ ] Test Case 7: State Management
- [ ] Test Case 8: Integration với Automatic Trigger
- [ ] Test Case 9: Permission và Security
- [ ] Test Case 10: Error Recovery

## Regression Testing

Sau mỗi lần cập nhật workflow, chạy lại:
1. Test Case 1 (basic functionality)
2. Test Case 7 (state management)  
3. Test Case 8 (integration)

Đảm bảo không break existing automatic trigger functionality.
