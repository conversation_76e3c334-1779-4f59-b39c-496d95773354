# Hướng dẫn sử dụng Manual Trigger cho Requirements Review

## Tổng quan

Workflow "Đánh giá thay đổi yêu cầu nghiệp vụ" đã được cải tiến để hỗ trợ trigger thủ công (manual trigger) thông qua GitHub Actions UI. Điều này cho phép bạn chạy workflow bất kỳ lúc nào để quét và đánh giá các thay đổi requirements mà không cần phải push code hoặc tạo PR.

## Các chế độ quét (Scan Modes)

### 1. Auto Mode (Mặc định)
- **Mô tả**: Tự động quét từ lần scan thành công cuối cùng đến commit hiện tại
- **Sử dụng khi**: <PERSON><PERSON><PERSON> quét các thay đổi mới từ lần review cuối cùng
- **Ưu điểm**: Tr<PERSON>h duplicate processing, chỉ quét thay đổi mới

### 2. Full Mode
- **Mô tả**: <PERSON><PERSON><PERSON> to<PERSON><PERSON> bộ từ commit đầu tiên đến HEAD
- **Sử dụng khi**: Muốn review lại toàn bộ requirements từ đầu
- **Lưu ý**: Có thể tạo ra issue với rất nhiều thay đổi

### 3. Custom Mode
- **Mô tả**: Quét trong khoảng commit tùy chỉnh
- **Sử dụng khi**: Muốn review một khoảng thời gian cụ thể
- **Yêu cầu**: Phải cung cấp commit bắt đầu và kết thúc

## Cách sử dụng Manual Trigger

### Bước 1: Truy cập GitHub Actions
1. Vào repository trên GitHub
2. Click tab "Actions"
3. Tìm workflow "Đánh giá thay đổi yêu cầu nghiệp vụ"
4. Click vào workflow name

### Bước 2: Trigger Manual
1. Click nút "Run workflow" (góc phải)
2. Chọn branch (thường là `main` hoặc `develop`)
3. Cấu hình các parameters:

#### Parameters chi tiết:

**Chế độ quét (scan_mode)**
- `auto`: Quét từ lần scan cuối cùng (khuyến nghị)
- `full`: Quét toàn bộ từ đầu
- `custom`: Quét khoảng commit tùy chỉnh

**Commit bắt đầu (from_commit)** - Chỉ dùng cho chế độ `custom`
- Nhập SHA commit bắt đầu (ví dụ: `abc123def`)
- Có thể dùng branch name hoặc tag

**Commit kết thúc (to_commit)** - Chỉ dùng cho chế độ `custom`
- Mặc định là `HEAD`
- Có thể nhập SHA commit cụ thể

**Bắt buộc tạo issue (force_create_issue)**
- `false` (mặc định): Chỉ tạo issue khi có thay đổi
- `true`: Tạo issue ngay cả khi không có thay đổi

### Bước 3: Chạy và theo dõi
1. Click "Run workflow"
2. Workflow sẽ xuất hiện trong danh sách runs
3. Click vào run để xem chi tiết và logs

## State Management

### Cách hoạt động
- Workflow sử dụng GitHub Actions cache để lưu trữ state
- State bao gồm: commit cuối cùng được scan, thời gian scan, loại trigger
- Mỗi lần tạo issue thành công, state sẽ được cập nhật

### Tránh duplicate processing
- Auto mode sẽ so sánh với state đã lưu
- Chỉ quét các commit mới từ lần scan cuối cùng
- Manual trigger và automatic trigger chia sẻ cùng state

## Ví dụ sử dụng

### Ví dụ 1: Quét thay đổi mới (Auto Mode)
```
Chế độ quét: auto
Commit bắt đầu: (để trống)
Commit kết thúc: (để trống)  
Bắt buộc tạo issue: false
```

### Ví dụ 2: Review toàn bộ requirements (Full Mode)
```
Chế độ quét: full
Commit bắt đầu: (để trống)
Commit kết thúc: (để trống)
Bắt buộc tạo issue: true
```

### Ví dụ 3: Review khoảng thời gian cụ thể (Custom Mode)
```
Chế độ quét: custom
Commit bắt đầu: abc123def
Commit kết thúc: xyz789ghi
Bắt buộc tạo issue: false
```

## Troubleshooting

### Lỗi thường gặp

**1. "Manual trigger requires from and to commits"**
- Nguyên nhân: Chọn custom mode nhưng không cung cấp commit range
- Giải pháp: Nhập commit bắt đầu hoặc chuyển sang auto/full mode

**2. Không tạo issue dù có thay đổi**
- Nguyên nhân: Thay đổi đã được scan trong lần trước
- Giải pháp: Sử dụng `force_create_issue: true`

**3. Issue quá lớn**
- Nguyên nhân: Quét quá nhiều commit cùng lúc
- Giải pháp: Sử dụng custom mode với khoảng commit nhỏ hơn

### Debug và monitoring
- Xem logs trong GitHub Actions run để hiểu workflow đang làm gì
- State được lưu trong cache với key `requirements-scan-state-{repository}`
- Mỗi step có logging chi tiết về commit range và scan mode

## Best Practices

1. **Sử dụng Auto Mode thường xuyên** để đảm bảo không bỏ sót thay đổi
2. **Full Mode chỉ dùng khi cần thiết** vì có thể tạo issue rất lớn
3. **Custom Mode cho review targeted** khi cần kiểm tra một feature cụ thể
4. **Kiểm tra state cache** nếu có vấn đề về duplicate processing
5. **Monitor workflow runs** để đảm bảo hoạt động đúng

## Tích hợp với quy trình hiện tại

Manual trigger không thay thế automatic trigger mà bổ sung:
- **Automatic trigger**: Vẫn hoạt động khi push/merge PR
- **Manual trigger**: Cho phép review on-demand
- **State sharing**: Cả hai loại trigger chia sẻ cùng state để tránh duplicate

Issue được tạo sẽ có cùng format và quy trình xử lý như trước.
