# Null Check cho Response Body trong REST Controller

## Phân tích lỗi

**<PERSON><PERSON> tả:** Sử dụng `response.getBody()` mà không kiểm tra null có thể gây ra `NullPointerException` trong runtime.

**Vị trí:** Method `calcLegacy` trong `HelloController.java`

**Nguyên nhân:** 
- Không kiểm tra `response` object có null hay không
- Chỉ kiểm tra `response.getBody()` mà không kiểm tra `response` trước đó
- Điều này có thể gây ra exception nếu `calculate()` method trả về null

**Rủi ro:**
- `NullPointerException` trong runtime
- Ảnh hưởng đến trải nghiệm người dùng
- C<PERSON> thể làm crash ứng dụng

## Giải pháp

### Code trước khi sửa:
```java
public CalculationResult calcLegacy(@RequestParam int left, @RequestParam int right) {
    log.warn("Legacy calc endpoint used - consider migrating to /calculate");
    ResponseEntity<CalculationResult> response = calculate(left, right);

    // Kiểm tra null để tránh NullPointerException
    if (response.getBody() == null) {
        throw new RuntimeException("Không thể lấy kết quả tính toán");
    }

    return response.getBody();
}
```

### Code sau khi sửa:
```java
public CalculationResult calcLegacy(@RequestParam int left, @RequestParam int right) {
    log.warn("Legacy calc endpoint used - consider migrating to /calculate");
    ResponseEntity<CalculationResult> response = calculate(left, right);

    // Kiểm tra response và body để tránh NullPointerException
    if (response == null || response.getBody() == null) {
        throw new RuntimeException("Không thể lấy kết quả tính toán");
    }

    return response.getBody();
}
```

### Cải tiến thực hiện:
1. **Kiểm tra đầy đủ:** Kiểm tra cả `response` và `response.getBody()` để đảm bảo an toàn
2. **Defensive programming:** Áp dụng nguyên tắc lập trình phòng thủ
3. **Error handling:** Ném exception với message tiếng Việt rõ ràng

### Best practices áp dụng:
- **Fail-fast principle:** Phát hiện lỗi sớm thay vì để lỗi lan truyền
- **Null safety:** Luôn kiểm tra null trước khi sử dụng object
- **Clean error messages:** Sử dụng message lỗi có ý nghĩa

## Kết quả

- ✅ Loại bỏ nguy cơ `NullPointerException`
- ✅ Code an toàn và robust hơn
- ✅ Tuân thủ best practices của Java
- ✅ Cải thiện error handling

## Lưu ý kỹ thuật

**Tại sao kiểm tra `response == null`:**
- Method `calculate()` có thể trả về null trong một số trường hợp edge case
- Defensive programming yêu cầu kiểm tra tất cả các khả năng

**Alternative approaches:**
```java
// Option 1: Sử dụng Optional
Optional.ofNullable(response)
    .map(ResponseEntity::getBody)
    .orElseThrow(() -> new RuntimeException("Không thể lấy kết quả tính toán"));

// Option 2: Sử dụng Objects.requireNonNull
Objects.requireNonNull(response, "Response không được null");
Objects.requireNonNull(response.getBody(), "Response body không được null");
```
