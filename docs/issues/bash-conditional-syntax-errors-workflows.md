# Bash Conditional Syntax Errors in GitHub Actions Workflows

## Phân tích lỗi

### Mô tả lỗi
Gặp lỗi "syntax error in conditional expression" (exit code 2) trong GitHub Actions workflows, cụ thể tại line 16 của reusable-lark-notification.yml. Lỗi này xảy ra do:

1. **Unquoted variables** trong bash conditionals
2. **Complex conditional expressions** không được tách riêng 
3. **GitHub Actions expressions** được sử dụng trực tiếp trong bash conditionals
4. **Regex patterns** với special characters gây confusion trong bash parsing

### Root Cause Analysis
- **GitHub Actions Context Injection**: Khi GitHub Actions inject values vào `${{ }}` expressions, nếu values chứa special characters hoặc empty, có thể gây bash syntax errors
- **Bash Conditional Best Practices**: Bash yêu cầu variables được quote đúng cách, đặc biệt khi chứa special characters
- **Complex Logic**: Multiple conditions trong single `[[ ]]` có thể gây parsing errors

### Lỗi cụ thể

#### 1. Regex Pattern trong Conditional Expression (Line 74)
```bash
# ❌ TRƯỚC - Có thể gây syntax error
if [[ "${{ inputs.event_created_at }}" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$ ]]; then
```

**Nguyên nhân**: 
- Space character trong regex pattern
- Direct GitHub Actions expression trong conditional
- Regex có thể fail nếu input chứa special characters

#### 2. Unquoted Variable trong Numeric Comparison (Line 192)
```bash
# ❌ TRƯỚC - Unquoted variable 
if [[ $HTTP_STATUS -ge 200 && $HTTP_STATUS -lt 300 ]]; then
```

**Nguyên nhân**: `$HTTP_STATUS` có thể empty hoặc chứa non-numeric values

#### 3. Complex AND Condition (Line 132)
```bash
# ❌ TRƯỚC - Complex condition trong single bracket
if [[ "$ADDITIONAL_INFO" != "{}" && -n "$ADDITIONAL_INFO" ]]; then
```

**Nguyên nhân**: Multiple conditions có thể gây parsing confusion

#### 4. Direct GitHub Expression trong Conditional (Line 149)
```bash
# ❌ TRƯỚC - Direct GitHub expression
if [[ -n "${{ inputs.content }}" && "${{ inputs.content }}" != "auto" ]]; then
```

**Nguyên nhân**: GitHub expressions có thể inject problematic values

## Giải pháp

### 1. Extract GitHub Expressions to Variables
```bash
# ✅ SAU - Extract to variable first
TIMESTAMP="${{ inputs.event_created_at }}"
if [[ "$TIMESTAMP" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}[T\ ][0-9]{2}:[0-9]{2}:[0-9]{2} ]]; then
```

**Lý do**: 
- Tách GitHub expression khỏi conditional logic
- Easier debugging và validation
- Avoid injection issues

### 2. Quote All Variables in Conditionals
```bash
# ✅ SAU - Properly quoted variable
if [[ "$HTTP_STATUS" -ge 200 && "$HTTP_STATUS" -lt 300 ]]; then
```

**Lý do**: Prevent word splitting và handle empty values

### 3. Split Complex Conditions
```bash
# ✅ SAU - Separate conditions
if [[ "$ADDITIONAL_INFO" != "{}" ]] && [[ -n "$ADDITIONAL_INFO" ]]; then
```

**Lý do**: 
- Clearer logic flow
- Better error isolation
- Avoid parsing ambiguity

### 4. Use Variable Assignment Pattern
```bash
# ✅ SAU - Variable assignment first
CONTENT_INPUT="${{ inputs.content }}"
if [[ -n "$CONTENT_INPUT" ]] && [[ "$CONTENT_INPUT" != "auto" ]]; then
```

**Lý do**: 
- Consistent pattern across workflow
- Better error handling
- Easier testing và debugging

### 5. Improve Regex Pattern
```bash
# ✅ SAU - More flexible regex pattern
if [[ "$TIMESTAMP" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}[T\ ][0-9]{2}:[0-9]{2}:[0-9]{2} ]]; then
```

**Lý do**: 
- Handle both ISO format (T separator) và space format
- More robust pattern matching
- Better compatibility với different timestamp formats

## Kết quả

### Trước khi sửa:
- ❌ "syntax error in conditional expression" (exit code 2)
- ❌ Workflow failing ở PR notification step
- ❌ Unquoted variables gây word splitting
- ❌ Complex conditions gây parsing errors
- ❌ Direct GitHub expressions trong bash conditionals

### Sau khi sửa:
- ✅ All bash conditionals pass syntax validation
- ✅ Proper variable quoting throughout workflows
- ✅ Separated complex logic into multiple conditions  
- ✅ GitHub expressions extracted to variables first
- ✅ Robust error handling cho edge cases

### Performance Impact:
- **Better reliability**: Reduced bash parsing failures
- **Clearer debugging**: Easier to trace conditional logic issues
- **Maintainability**: Consistent patterns across workflows

### Files được sửa:
1. `reusable-lark-notification.yml` - Main fixes
2. `reusable-flexible-runner.yml` - Environment verification conditionals

## Testing Strategy

### Manual Testing
```bash
# Test bash conditional syntax locally
TIMESTAMP="2024-01-01T10:00:00Z"
if [[ "$TIMESTAMP" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}[T\ ][0-9]{2}:[0-9]{2}:[0-9]{2} ]]; then
  echo "Valid timestamp format"
fi

# Test with empty values
HTTP_STATUS=""
if [[ "$HTTP_STATUS" -ge 200 && "$HTTP_STATUS" -lt 300 ]]; then
  echo "This should not cause syntax error"
fi
```

### Automated Testing
- GitHub Actions sẽ validate bash syntax khi execute
- Test với various input combinations
- Monitor workflow execution logs

## Best Practices Learned

1. **Always quote variables** trong bash conditionals: `"$VAR"` not `$VAR`
2. **Extract GitHub expressions** to variables before using trong conditionals
3. **Split complex conditions** thành multiple separate checks
4. **Use consistent patterns** across all workflow files
5. **Test edge cases** như empty values, special characters
6. **Avoid regex trong conditionals** nếu possible, use separate validation

## Prevention Guidelines

1. **Variable Assignment First**: Always extract `${{ }}` expressions to shell variables
2. **Quote Everything**: Quote all variables trong conditionals
3. **Simple Conditions**: Keep each conditional check simple và focused
4. **Validate Inputs**: Add input validation trước khi sử dụng trong conditionals
5. **Consistent Patterns**: Use same conditional patterns across workflows
6. **Testing**: Test workflows với empty, null, và special character inputs

## Related Issues

- Có thể liên quan đến các workflows khác sử dụng bash conditionals
- Review tất cả workflows để ensure consistent bash syntax
- Consider tạo shared bash utilities cho common conditional checks