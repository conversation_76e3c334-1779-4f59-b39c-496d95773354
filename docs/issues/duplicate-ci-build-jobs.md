# Sửa lỗi: Duplicate CI Build Jobs

## Phân tích lỗi

### Vấn đề được báo cáo
- CI Build jobs chạy duplicate (mỗi backend/frontend chạy 2 lần)
- Build Status Summary cũng chạy 2 lần
- <PERSON><PERSON>y lãng phí tài nguyên CI/CD và làm chậm pipeline

### Nguyên nhân gốc rễ
CI Workflow được trigger bởi cả 2 events cùng lúc:
1. **Push event**: Khi push code lên feature branch
2. **Pull Request event**: <PERSON>hi tạo/update PR từ feature branch đó

**Kịch bản reproduce:**
```text
1. Developer push code lên feature/abc
2. GitHub tự động trigger:
   - Push event (feature/abc) → CI Build
   - PR event (feature/abc → main) → CI Build (duplicate)
```

### Bằng chứng
Từ GitHub Actions log có thể thấy:
- CI Build / Build Frontend (NextJS TypeScript) (pull_request) 
- CI Build / Build Frontend (NextJS TypeScript) (push)
- CI Build / Build Status Summary (pull_request)
- CI Build / Build Status Summary (push)

## Giải pháp

### Cải tiến Trigger Strategy

#### Trước (có vấn đề):
```yaml
on:
  push:
    branches: [ main, develop, feature/* ]  # ← Bao gồm feature/*
  pull_request:
    branches: [ main, develop ]
```

#### Sau (đã sửa):
```yaml
on:
  push:
    branches: [ main, develop ]             # ← Chỉ protected branches
  pull_request:
    branches: [ main, develop ]
    types: [ opened, synchronize, reopened, ready_for_review ]
```

### Cải tiến Concurrency Group

#### Trước:
```yaml
concurrency:
  group: ci-build-${{ github.ref }}
```

#### Sau:
```yaml
concurrency:
  group: ci-build-${{ github.event_name }}-${{ github.ref }}
  cancel-in-progress: true
```

### Logic Enhancement

Thêm logging chi tiết để debug:
```bash
echo "Event: ${{ github.event_name }}"
echo "Ref: ${{ github.ref }}"
echo "Base Ref: ${{ github.base_ref }}"
```

## Kết quả

### ✅ Sau khi sửa
- **Push events**: Chỉ trigger cho main/develop (protected branches)
- **PR events**: Trigger cho tất cả PRs targeting main/develop
- **No duplicate**: Mỗi feature branch chỉ trigger qua PR event
- **Resource optimization**: Giảm 50% số lượng CI runs

### 📊 So sánh trước/sau

| Scenario            | Trước            | Sau          |
| ------------------- | ---------------- | ------------ |
| Push to feature/abc | Push + PR events | Chỉ PR event |
| Push to main        | Push event       | Push event   |
| Update PR           | PR event         | PR event     |
| **Total CI runs**   | **2x**           | **1x**       |

### 🎯 Lợi ích
- Tiết kiệm 50% tài nguyên CI/CD
- Tăng tốc feedback loop cho developers
- Giảm confusion trong GitHub Actions UI
- Tối ưu hóa workflow strategy

## Best Practice áp dụng

### Trigger Strategy
1. **Protected branches** (main/develop): Chỉ push events
2. **Feature branches**: Chỉ PR events
3. **Draft PRs**: Skip để tiết kiệm tài nguyên

### Concurrency Management
1. Sử dụng `event_name` trong concurrency group
2. Enable `cancel-in-progress` cho feature branches
3. Logging chi tiết để debug

### Monitoring
1. Monitor số lượng CI runs hàng ngày
2. Track resource consumption
3. Alert khi có duplicate patterns

## Tags
`ci-cd`, `duplicate-jobs`, `github-actions`, `workflow-optimization`, `resource-management`
