# CMS - Content Management System

## 📋 Mô tả dự án

Hệ thống **Content Management System (CMS)** đ<PERSON><PERSON><PERSON> thiết kế để hỗ trợ biên tập, phê duyệt và phân phối nội dung đa kênh (website, mobile app, mạng xã hội) một cách linh hoạt, bảo mật và tối ưu trải nghiệm người dùng.

### Mục tiêu kinh doanh
- Tăng tốc độ xuất bản tin bài ≥ **30%** so với quy trình thủ công
- <PERSON><PERSON><PERSON> bảo **100%** nội dung tuân thủ quy trình phê duyệt 2 bước
- Rút ngắn thời gian tìm kiếm nội dung & media xuống < **3 giây**

### Tính năng chính
- Qu<PERSON>n lý kênh & chuy<PERSON><PERSON> mục
- <PERSON>i<PERSON>n tập nội dung với rich text editor
- <PERSON>uy trình phê duyệt đa cấp
- Quản lý media và file
- Hiển thị & tối ưu SEO
- Giao diện responsive cho admin và web

## 🏗️ Kiến trúc hệ thống

### Tech Stack

#### Backend
- **Java 21** với **Spring Boot 3.2.0**
- **PostgreSQL** (Production) / **H2** (Development)
- **Keycloak** (Authentication & Authorization)
- **MinIO** (File Storage)
- **Redis** (Cache)
- **Maven** (Build Tool)

#### Frontend
- **TypeScript** với **React 19**
- **NextJS 15.2.3** (App Router)
- **TailwindCSS 4.0.8** (Styling)
- **Shadcn/UI** (UI Components)
- **Zustand 5.0.6** (State Management)
- **Turbo** (Build System)
- **PNPM 10.4.1** (Package Manager)

#### DevOps & Infrastructure
- **GitHub Actions** (CI/CD)
- **Docker** (Containerization)
- **Kubernetes** (Orchestration)

### Kiến trúc tổng quan
```
┌─────────────────┐    ┌─────────────────┐
│   Admin App     │    │    Web App      │
│   (NextJS)      │    │   (NextJS)      │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     │
          ┌─────────────────────┐
          │   Backend API       │
          │  (Spring Boot)      │
          └─────────┬───────────┘
                    │
    ┌───────────────┼───────────────┐
    │               │               │
┌───▼───┐    ┌─────▼─────┐    ┌───▼───┐
│PostgreSQL│  │   Redis   │    │ MinIO │
│Database  │  │  (Cache)  │    │(Files)│
└─────────┘  └───────────┘    └───────┘
```

## 📁 Cấu trúc thư mục

```
cms/
├── src/
│   ├── backend/                    # Spring Boot Backend
│   │   ├── src/main/java/com/terragon/
│   │   │   ├── domain/            # Domain Layer (DDD)
│   │   │   ├── application/       # Application Layer
│   │   │   ├── infrastructure/    # Infrastructure Layer
│   │   │   └── presentation/      # Presentation Layer (Controllers)
│   │   ├── pom.xml               # Maven configuration
│   │   └── README.md             # Backend documentation
│   │
│   └── frontend/                  # Frontend Monorepo
│       ├── apps/
│       │   ├── admin/            # Admin Dashboard (NextJS)
│       │   └── web/              # Public Web App (NextJS)
│       ├── packages/
│       │   ├── api-client/       # API client library
│       │   ├── auth/             # Authentication utilities
│       │   ├── lib/              # Shared utilities
│       │   ├── store/            # Zustand stores
│       │   ├── types/            # TypeScript definitions
│       │   └── ui/               # Shadcn UI components
│       ├── pnpm-workspace.yaml   # PNPM workspace config
│       ├── turbo.json            # Turbo build config
│       └── README.md             # Frontend documentation
│
├── docs/                          # Project Documentation
│   ├── architecture-design/      # System architecture docs
│   ├── requirements/             # Business requirements
│   ├── guidelines/               # Development guidelines
│   ├── issues/                   # Issue tracking & solutions
│   └── detailed-design/          # Detailed design docs
│
├── .github/
│   └── workflows/                # CI/CD GitHub Actions
│       ├── ci-build.yml          # Build pipeline
│       └── requirements-change-review.yml  # Requirements notification
│
├── AGENTS.md                     # AI agents configuration
├── CLAUDE.md                     # Claude AI instructions
├── GEMINI.md                     # Gemini AI instructions
└── README.md                     # This file
```

## 📚 Tài liệu quan trọng

### Architecture Design (`docs/architecture-design/`)
- **`system-architecture.md`** - Thiết kế kiến trúc tổng thể hệ thống
- **`high-level-design.md`** - Thiết kế chi tiết components và modules
- **`component-architecture.md`** - Kiến trúc từng component
- **`ddd-clean-architecture.md`** - Hướng dẫn DDD/Clean Architecture

### Requirements (`docs/requirements/`)
- **`cms_product_backlog.md`** - Product backlog và user stories chi tiết

### Guidelines (`docs/guidelines/`)
- **`github-ci-cd-and-notifications.md`** - Hướng dẫn CI/CD và notification
- **`vietnamese-comments-guideline.md`** - Quy tắc viết comment tiếng Việt

### Issues Tracking (`docs/issues/`)
- **`path-based-selective-ci-implementation.md`** - Giải pháp selective CI building
- **`summary.md`** - Tóm tắt các issues đã giải quyết

### Detailed Design (`docs/detailed-design/`)
- **Template và hướng dẫn** cho việc tạo tài liệu thiết kế chi tiết

## 🚀 Hướng dẫn bắt đầu

### Yêu cầu hệ thống
- **Java 21+**
- **Node.js 20+ LTS**
- **PNPM 10+**
- **Maven 3.8+**
- **PostgreSQL 13+** (Production)
- **Docker Desktop** (Optional)

### Cài đặt và chạy

#### 1. Clone repository
```bash
git clone <repository-url>
cd cms
```

#### 2. Backend Setup
```bash
cd src/backend
mvn clean install

# Development mode (H2 database)
mvn spring-boot:run -Dspring.profiles.active=dev

# Production mode (PostgreSQL)
mvn spring-boot:run
```

#### 3. Frontend Setup
```bash
cd src/frontend
pnpm install

# Chạy Admin app
cd apps/admin
pnpm dev

# Chạy Web app (terminal mới)
cd apps/web  
pnpm dev
```

#### 4. Truy cập ứng dụng
- **Admin Dashboard**: http://localhost:3000
- **Web Application**: http://localhost:3001
- **Backend API**: http://localhost:8080

## 🤖 AI Tools đang sử dụng

Dự án tích hợp các AI assistant để hỗ trợ development:

- **Claude (Anthropic)** - Code review, architecture design, documentation
- **Gemini (Google)** - Code generation, debugging, optimization
- **GitHub Copilot** - Code completion, suggestion
- **Augment Code** - Intelligent code assistance

Các AI tools được cấu hình với guidelines cụ thể trong các file `CLAUDE.md`, `GEMINI.md`, `AGENTS.md`.

## 🔗 Liên kết dự án

### Communication & Collaboration
- **Chat Group**: [Lark Chat](https://applink.larksuite.com/client/chat/chatter/add_by_link?link_token=71dp7bb8-1a53-4aef-b704-46115eq951hd)
- **Document Storage**: [Lark Drive](https://ospgroup.sg.larksuite.com/drive/folder/WrrUfciBYlj1vhd6B9LlwsVqgkg)
- **Task Management**: [Lark Tasklist](https://applink.larksuite.com/client/todo/task_list?guid=cade8d86-83ce-4d35-abfe-659a4b460398)

### AI & Knowledge Management
- **NotebookLM**: [Project Knowledge Base](https://notebooklm.google.com/notebook/cc46c1d6-cc93-450a-b529-92ccda9a6626)

## 📖 Liên kết tham khảo

### Tài liệu chi tiết
- [Backend Documentation](src/backend/README.md)
- [Frontend Documentation](src/frontend/README.md)
- [System Architecture](docs/architecture-design/system-architecture.md)
- [Product Backlog](docs/requirements/cms_product_backlog.md)

### Development Guidelines
- [DDD/Clean Architecture Guide](docs/architecture-design/ddd-clean-architecture.md)
- [Vietnamese Comments Guideline](docs/guidelines/vietnamese-comments-guideline.md)
- [CI/CD Guidelines](docs/guidelines/github-ci-cd-and-notifications.md)

### External Resources
- [Spring Boot Documentation](https://spring.io/projects/spring-boot)
- [NextJS Documentation](https://nextjs.org/docs)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)
- [Shadcn/UI Components](https://ui.shadcn.com/)

---

**Lưu ý**: Dự án đang trong giai đoạn phát triển. Vui lòng tham khảo các tài liệu trong thư mục `docs/` để có thông tin chi tiết và cập nhật nhất.

Để bắt đầu contribute, hãy đọc [Architecture Design](docs/architecture-design/system-architecture.md) và [Product Backlog](docs/requirements/cms_product_backlog.md) trước.
