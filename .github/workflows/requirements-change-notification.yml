name: Business Requirements Change Notification

on:
  issues:
    types: [opened]

concurrency:
  group: requirements-notification-${{ github.event.issue.number }}
  cancel-in-progress: true

jobs:
  notify-lark:
    name: Lark Requirements Change Notification
    uses: ./.github/workflows/reusable-lark-notification.yml
    if: contains(github.event.issue.labels.*.name, 'yêu-cầu-nghiệp-vụ')
    with:
      notification_type: 'issue'
      title: ${{ github.event.issue.title }}
      content: 'auto'
      event_url: ${{ github.event.issue.html_url }}
      event_number: ${{ github.event.issue.number }}
      event_user: ${{ github.event.issue.user.login }}
      event_created_at: ${{ github.event.issue.created_at }}
      additional_info: |
        {
          "status": "Cần đánh giá"
        }
      header_template: 'orange'
    secrets:
      LARK_WEBHOOK_URL: ${{ secrets.LARK_CHAT_GROUP_NOTIFICATION }}
