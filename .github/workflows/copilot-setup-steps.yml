name: "Copilot Setup Steps"

# Tự động chạy các setup steps khi có thay đổi để dễ dàng validation, và
# cho phép manual testing thông qua tab "Actions" của repository
on:
  workflow_dispatch:
  push:
    paths:
      - .github/workflows/copilot-setup-steps.yml
  pull_request:
    paths:
      - .github/workflows/copilot-setup-steps.yml

jobs:
  # Job BẮT BUỘC phải có tên `copilot-setup-steps` nếu không Copilot sẽ không nhận diện được.
  copilot-setup-steps:
    name: Copilot Environment Setup
    uses: ./.github/workflows/reusable-flexible-runner.yml
    with:
      job_name: 'Copilot Setup Steps'
      setup_java: true
      java_version: '21'
      setup_node: true
      node_version: '20'
      setup_pnpm: true
      pnpm_version: '10'
      install_dependencies: 'both'
      run_command: |
        echo "🤖 Copilot environment setup completed!"
        echo "✅ All dependencies installed and ready for Copilot operations"
        echo "📝 Environment is now ready for code analysis and AI assistance"
