name: "<PERSON><PERSON><PERSON> gi<PERSON> thay đổi yêu c<PERSON>u nghiệp vụ"

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'docs/requirements/**'
  pull_request:
    types: [closed]
    branches:
      - main
      - develop
    paths:
      - 'docs/requirements/**'

permissions:
  contents: read
  issues: write
  pull-requests: write

jobs:
  create-requirements-review-issue:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.merged == true)
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Analyze requirements changes
        id: requirements-analysis
        uses: ./.github/actions/requirements-review
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          event-name: ${{ github.event_name }}
          before-commit: ${{ github.event.before }}
          current-commit: ${{ github.sha }}
          pr-base-sha: ${{ github.event.pull_request.base.sha }}
          pr-head-sha: ${{ github.event.pull_request.head.sha }}

      - name: Create issue from template
        if: steps.requirements-analysis.outputs.has-changes == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const previousCommit = '${{ steps.requirements-analysis.outputs.previous-commit }}';
            const currentCommit = '${{ steps.requirements-analysis.outputs.current-commit }}';
            const changedFiles = `${{ steps.requirements-analysis.outputs.changed-files }}`;
            const diffContent = `${{ steps.requirements-analysis.outputs.diff-content }}`;
            
            const currentDate = new Date().toLocaleDateString('vi-VN');
            const currentDateTime = new Date().toLocaleString('vi-VN');
            
            const previousCommitLink = `${context.payload.repository.html_url}/commit/${previousCommit}`;
            const currentCommitLink = `${context.payload.repository.html_url}/commit/${currentCommit}`;
            
            const committer = context.payload.head_commit?.committer?.name || 
                             context.payload.pull_request?.user?.login || 
                             'Unknown';
            
            const issueTitle = `[REVIEW] Đánh giá thay đổi yêu cầu nghiệp vụ - ${currentDate}`;
            
            // Create issue body with proper escaping
            const issueBody = [
              '## 📋 Thông tin thay đổi',
              '',
              `**Thời gian phát hiện:** ${currentDateTime}`,
              `**Người commit:** ${committer}`,
              '',
              '> 🤖 **@copilot** và **Jules**: Vui lòng đánh giá tác động của các thay đổi yêu cầu nghiệp vụ này.',
              '',
              '## Nội dung thay đổi',
              '',
              `**Commit trước:** \`${previousCommit.substring(0, 7)}\` ([Xem chi tiết](${previousCommitLink}))`,
              `**Commit sau:** \`${currentCommit.substring(0, 7)}\` ([Xem chi tiết](${currentCommitLink}))`,
              '',
              '### Tệp tin bị thay đổi:',
              changedFiles,
              '',
              '### Chi tiết thay đổi:',
              '```diff',
              diffContent,
              '```',
              '',
              '## 🔍 Cần đánh giá',
              '',
              '### 1. Loại thay đổi',
              '- [ ] Tính năng mới',
              '- [ ] Sửa đổi tính năng cũ',
              '- [ ] Chưa phân loại',
              '',
              '### 2. Phân tích tác động',
              '**Thay đổi này sẽ ảnh hưởng đến những user story/epic nào?**',
              '',
              '_[Vui lòng điền thông tin]_',
              '',
              '### 3. Công việc cần thực hiện theo vai trò',
              '',
              '#### 👨‍🎨 Designer',
              '- [ ] Cần cập nhật UI/UX design',
              '- [ ] Cần cập nhật wireframe',
              '- [ ] Cần tạo prototype mới',
              '- [ ] Không cần thay đổi',
              '',
              '**Chi tiết công việc:**',
              '_[Vui lòng mô tả cụ thể]_',
              '',
              '#### 📊 System Analyst (SA)',
              '- [ ] Cần cập nhật tài liệu phân tích',
              '- [ ] Cần xem xét lại use case',
              '- [ ] Cần cập nhật business rules',
              '- [ ] Không cần thay đổi',
              '',
              '**Chi tiết công việc:**',
              '_[Vui lòng mô tả cụ thể]_',
              '',
              '#### 💻 Developer',
              '- [ ] Cần cập nhật code',
              '- [ ] Cần thay đổi API',
              '- [ ] Cần cập nhật database schema',
              '- [ ] Không cần thay đổi',
              '',
              '**Chi tiết công việc:**',
              '_[Vui lòng mô tả cụ thể]_',
              '',
              '#### 🧪 Tester',
              '- [ ] Cần cập nhật test case',
              '- [ ] Cần cập nhật test plan',
              '- [ ] Cần viết automation test mới',
              '- [ ] Không cần thay đổi',
              '',
              '**Chi tiết công việc:**',
              '_[Vui lòng mô tả cụ thể]_',
              '',
              '### 4. Mức độ ưu tiên',
              '- [ ] Thấp',
              '- [ ] Trung bình',
              '- [ ] Cao',
              '- [ ] Khẩn cấp',
              '',
              '### 5. Bước tiếp theo',
              '- [ ] Schedule meeting với stakeholders',
              '- [ ] Review và approve design changes',
              '- [ ] Cập nhật development plan',
              '- [ ] Thực hiện testing',
              '- [ ] Khác: _[Vui lòng mô tả]_',
              '',
              '### 6. Ghi chú bổ sung',
              '_[Thông tin bổ sung hoặc các rủi ro cần lưu ý]_',
              '',
              '---',
              '**🤖 Issue này được tạo tự động bởi GitHub Actions**',
              '',
              '**Assignees:**',
              '- 🤖 **Copilot**: Thực hiện phân tích tự động và đưa ra gợi ý',
              '- 🔍 **Jules**: Đánh giá và xác thực kết quả từ AI'
            ].join('\n');

            const issue = await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: issueTitle,
              body: issueBody,
              labels: ['yêu-cầu-nghiệp-vụ', 'requirements-review', 'business-analysis', 'auto-generated', 'assign-to-copilot', 'assign-to-jules']
            });
            
            console.log(`Created issue: ${issue.data.html_url}`);

      - name: Add comment to PR (if applicable)
        if: github.event_name == 'pull_request' && steps.requirements-analysis.outputs.has-changes == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const comment = [
              '## 🔔 Thông báo: Phát hiện thay đổi yêu cầu nghiệp vụ',
              '',
              'Hệ thống đã phát hiện có thay đổi trong thư mục `docs/requirements`.',
              '',
              'Một issue đánh giá tác động đã được tạo tự động để review những thay đổi này:',
              '- 📋 **Issue:** Sẽ được tạo sau khi PR được merge',
              '- 🎯 **Mục đích:** Đánh giá tác động đến các user story/epic và phân công công việc cho các role',
              '- ⏰ **Thời gian:** Ngay sau khi merge',
              '',
              '**Lưu ý:** Vui lòng kiểm tra và xử lý issue review sau khi merge PR này.'
            ].join('\n');

            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
